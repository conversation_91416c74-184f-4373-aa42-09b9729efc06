<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Sidebar -->
    <aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out">
      <!-- Sidebar Header -->
      <div class="flex items-center justify-center h-16 px-4 bg-primary-600 text-white">
        <UIcon name="lucide:wrench" class="w-8 h-8 mr-2" />
        <h1 class="text-xl font-bold">OMS Admin</h1>
      </div>
      
      <!-- Navigation Menu -->
      <nav class="mt-8 px-4">
        <ul class="space-y-2">
          <li v-for="item in navMenuItems" :key="item.id">
            <NuxtLink 
              :to="item.route"
              class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              :class="{ 'bg-primary-50 text-primary-600 border-r-4 border-primary-600': item.isActive }"
            >
              <UIcon :name="item.icon" class="w-5 h-5 mr-3" />
              <span class="font-medium">{{ item.label }}</span>
              <!-- Badge for notifications -->
              <UBadge 
                v-if="item.badge" 
                :label="item.badge.toString()" 
                color="error" 
                variant="solid"
                class="ml-auto"
              />
            </NuxtLink>
          </li>
        </ul>
      </nav>
      
      <!-- Sidebar Footer -->
      <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div class="flex items-center">
          <UAvatar 
            :alt="currentUser.firstName + ' ' + currentUser.lastName"
            size="sm"
            class="mr-3"
          />
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">
              {{ currentUser.firstName }} {{ currentUser.lastName }}
            </p>
            <p class="text-xs text-gray-500 truncate">
              {{ currentUser.role }}
            </p>
          </div>
        </div>
      </div>
    </aside>

    <!-- Main Content Area -->
    <div class="ml-64">
      <!-- Header -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- Page Title -->
            <div>
              <h2 class="text-2xl font-bold text-gray-900">{{ pageTitle }}</h2>
              <p class="text-sm text-gray-500 mt-1">{{ pageDescription }}</p>
            </div>
            
            <!-- Header Actions -->
            <div class="flex items-center space-x-4">
              <!-- Notifications -->
              <UButton 
                icon="lucide:bell" 
                variant="ghost" 
                size="sm"
                :badge="notificationCount"
                @click="showNotifications = !showNotifications"
              />
              
              <!-- User Menu -->
              <UDropdownMenu :items="userMenuItems" :popper="{ placement: 'bottom-end' }">
                <UAvatar 
                  :alt="currentUser.firstName + ' ' + currentUser.lastName"
                  size="sm"
                  class="cursor-pointer"
                />
                
                <template #account="{ item }">
                  <div class="text-left">
                    <p class="font-medium">{{ item.label }}</p>
                    <p class="text-xs text-gray-500">{{ item.description }}</p>
                  </div>
                </template>
              </UDropdownMenu>
            </div>
          </div>
        </div>
      </header>

      <!-- Page Content -->
      <main class="p-6">
        <!-- Breadcrumb Navigation -->
        <nav class="mb-6" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-2 text-sm">
            <li>
              <NuxtLink to="/" class="text-gray-500 hover:text-gray-700">
                Home
              </NuxtLink>
            </li>
            <li v-for="(crumb, index) in breadcrumbs" :key="index" class="flex items-center">
              <UIcon name="lucide:chevron-right" class="w-4 h-4 mx-2 text-gray-400" />
              <NuxtLink 
                v-if="crumb.to" 
                :to="crumb.to"
                class="text-gray-500 hover:text-gray-700"
              >
                {{ crumb.label }}
              </NuxtLink>
              <span v-else class="text-gray-900">{{ crumb.label }}</span>
            </li>
          </ol>
        </nav>

        <!-- Main Content Slot -->
        <div class="bg-white rounded-lg shadow-sm">
          <slot />
        </div>
      </main>
    </div>

    <!-- Notification Panel -->
    <div v-if="showNotifications" class="fixed inset-0 z-50 overflow-hidden">
      <div class="absolute inset-0 bg-black bg-opacity-50" @click="showNotifications = false"></div>
      <div class="absolute right-0 top-0 h-full w-96 bg-white shadow-xl">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Notifications</h3>
            <UButton 
              icon="lucide:x" 
              variant="ghost" 
              size="sm"
              @click="showNotifications = false"
            />
          </div>
          
          <!-- Notification List -->
          <div class="space-y-3">
            <div v-for="notification in recentNotifications" :key="notification.id" 
                 class="p-3 bg-gray-50 rounded-lg">
              <p class="text-sm font-medium">{{ notification.title }}</p>
              <p class="text-xs text-gray-500 mt-1">{{ notification.message }}</p>
              <p class="text-xs text-gray-400 mt-2">{{ formatTimestamp(notification.timestamp) }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { mockNavMenuItems, mockUsers, mockActivities } from '~/data/mockData';
import type { NavMenuItem, User, Activity } from '~/types';

// Layout configuration removed - not needed in layout files

// Component state
const showNotifications = ref(false);
const notificationCount = ref(3);

// Mock current user (in real app, this would come from auth store)
const currentUser: User = mockUsers[0]!; // Using admin user as default

// Navigation menu items
// Get real issue count for sidebar badge
const { issues } = useIssues()

// Create dynamic nav menu with real issue count
const navMenuItems = computed(() => {
  return mockNavMenuItems.map(item => {
    if (item.id === 'issues') {
      return {
        ...item,
        badge: issues.value.length // Use real issue count instead of mock
      }
    }
    return item
  })
})

// User menu items for dropdown
const userMenuItems = [
  [{
    label: currentUser.firstName + ' ' + currentUser.lastName,
    description: currentUser.email,
    slot: 'account',
    disabled: true
  }], 
  [{
    label: 'Profile',
    icon: 'lucide:user',
    to: '/profile'
  }, {
    label: 'Settings',
    icon: 'lucide:settings',
    to: '/settings'
  }], 
  [{
    label: 'Logout',
    icon: 'lucide:log-out',
    click: () => {
      // Handle logout
      console.log('Logout clicked');
    }
  }]
];

// Page metadata (can be overridden by individual pages)
const pageTitle = ref('Dashboard');
const pageDescription = ref('Overview of system status and recent activities');

// Breadcrumb navigation
const breadcrumbs = ref<Array<{ label: string, to: string | null }>>([
  { label: 'Dashboard', to: null }
]);

// Recent notifications from activities
const recentNotifications = computed(() => {
  return mockActivities.slice(0, 5).map(activity => ({
    id: activity.id,
    title: activity.action,
    message: activity.description,
    timestamp: activity.timestamp
  }));
});

/**
 * Format timestamp for display
 */
const formatTimestamp = (timestamp: Date): string => {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}m ago`;
  } else if (hours < 24) {
    return `${hours}h ago`;
  } else {
    return `${days}d ago`;
  }
};

// Provide functions to update page metadata
const setPageTitle = (title: string) => {
  pageTitle.value = title;
};

const setPageDescription = (description: string) => {
  pageDescription.value = description;
};

const setBreadcrumbs = (crumbs: Array<{ label: string, to?: string }>) => {
  breadcrumbs.value = crumbs.map(crumb => ({
    label: crumb.label,
    to: crumb.to || null
  }));
};

// Make functions available to child components
provide('setPageTitle', setPageTitle);
provide('setPageDescription', setPageDescription);
provide('setBreadcrumbs', setBreadcrumbs);
</script>

<style scoped>
/* Custom scrollbar for sidebar */
nav {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

nav::-webkit-scrollbar {
  width: 6px;
}

nav::-webkit-scrollbar-track {
  background: #f1f5f9;
}

nav::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

nav::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
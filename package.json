{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "1.5.2", "@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.15.0", "@nuxt/image": "1.10.0", "@nuxt/ui": "3.2.0", "@nuxtjs/supabase": "^1.5.3", "@supabase/supabase-js": "^2.50.4", "eslint": "^9.0.0", "nuxt": "^3.17.6", "typescript": "^5.6.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}}
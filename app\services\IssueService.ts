/**
 * Issue CRUD operations service class
 * Handles business logic for issue management with admin privileges
 * Provides comprehensive issue operations for the desktop admin interface
 */

import type { 
  IssueReport, 
  IssueReportInput, 
  Photo,
  IssueUpdate
} from '~/types'
import { 
  IssueCategory, 
  IssueSeverity, 
  IssueStatus
} from '~/types'

export class IssueService {
  private supabase: any
  
  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  /**
   * Get issue statistics for dashboard
   * Returns aggregated data for admin overview
   */
  async getIssueStatistics(): Promise<{
    success: boolean
    data?: {
      total: number
      bySeverity: Record<IssueSeverity, number>
      byStatus: Record<IssueStatus, number>
      byCategory: Record<IssueCategory, number>
      recentCount: number
      criticalIssues: IssueReport[]
    }
    error?: string
  }> {
    try {
      console.log('Fetching issue statistics...')
      
      // Get total count
      const { count: totalCount, error: countError } = await this.supabase
        .from('reports')
        .select('*', { count: 'exact', head: true })
      
      if (countError) {
        return { success: false, error: countError.message }
      }
      
      // Get all issues for aggregation (in production, use database aggregation)
      const { data: allIssues, error: issuesError } = await this.supabase
        .from('reports')
        .select('severity, status, created_at, title, description, id, location')
        .order('created_at', { ascending: false })
      
      if (issuesError) {
        return { success: false, error: issuesError.message }
      }
      
      // Aggregate data
      const bySeverity: Record<IssueSeverity, number> = {
        [IssueSeverity.High]: 0,
        [IssueSeverity.Medium]: 0,
        [IssueSeverity.Low]: 0
      }
      
      const byStatus: Record<IssueStatus, number> = {
        [IssueStatus.New]: 0,
        [IssueStatus.Assigned]: 0,
        [IssueStatus.InProgress]: 0,
        [IssueStatus.Resolved]: 0
      }
      
      const byCategory: Record<IssueCategory, number> = {
        [IssueCategory.Docket]: 0,
        [IssueCategory.Vandalism]: 0,
        [IssueCategory.Corrective]: 0,
        [IssueCategory.Preventive]: 0,
        [IssueCategory.Audit]: 0
      }
      
      // Get recent issues (last 7 days)
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      let recentCount = 0
      
      // Find critical issues (High severity, not resolved)
      const criticalIssues: any[] = []
      
      allIssues?.forEach((issue: any) => {
        // Count by severity
        if (issue.severity in bySeverity) {
          bySeverity[issue.severity as IssueSeverity]++
        }
        
        // Count by status
        if (issue.status in byStatus) {
          byStatus[issue.status as IssueStatus]++
        }
        
        // Count by category (use default since category column doesn't exist yet)
        // For now, categorize based on title/description keywords
        const categoryFromContent = this.inferCategoryFromContent(issue.title, issue.description)
        if (categoryFromContent in byCategory) {
          byCategory[categoryFromContent]++
        }
        
        // Count recent issues
        if (new Date(issue.created_at) > weekAgo) {
          recentCount++
        }
        
        // Collect critical issues
        if (issue.severity === IssueSeverity.High && issue.status !== IssueStatus.Resolved) {
          criticalIssues.push({
            id: issue.id,
            title: issue.title,
            description: issue.description,
            severity: issue.severity,
            status: issue.status,
            siteId: issue.location,
            timestamp: issue.created_at
          })
        }
      })
      
      console.log('✅ Issue statistics fetched successfully')
      
      return {
        success: true,
        data: {
          total: totalCount || 0,
          bySeverity,
          byStatus,
          byCategory,
          recentCount,
          criticalIssues: criticalIssues.slice(0, 5) // Top 5 critical issues
        }
      }
    } catch (err) {
      console.error('Error fetching issue statistics:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch issue statistics' 
      }
    }
  }

  /**
   * Create issue with validation and business logic
   * Handles proper data validation and defaults
   */
  async createIssueWithValidation(issueInput: IssueReportInput): Promise<{
    success: boolean
    data?: IssueReport
    error?: string
  }> {
    try {
      console.log('Creating issue with validation:', issueInput.title)
      
      // Validate required fields
      const validation = this.validateIssueInput(issueInput)
      if (!validation.isValid) {
        return { success: false, error: validation.error }
      }
      
      // Generate unique ID
      const issueId = this.generateIssueId()
      
      // Prepare issue data for database
      const issueData = {
        id: issueId,
        device_id: 'desktop-admin',
        title: issueInput.title.trim(),
        description: issueInput.description?.trim() || '',
        location: issueInput.siteId, // Maps to siteId
        severity: issueInput.severity,
        status: issueInput.status || IssueStatus.New,
        // category: issueInput.category, // Column doesn't exist in current schema
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sync_status: 'synced'
      }
      
      // Insert to database
      const { data, error } = await this.supabase
        .from('reports')
        .insert([issueData])
        .select()
        .single()
      
      if (error) {
        console.error('Database error creating issue:', error)
        return { success: false, error: error.message }
      }
      
      // Transform to IssueReport format
      const createdIssue: IssueReport = {
        id: data.id,
        title: data.title,
        category: issueInput.category, // Use input category since DB doesn't store it yet
        description: data.description,
        siteId: data.location,
        timestamp: data.created_at,
        severity: data.severity,
        status: data.status,
        photos: issueInput.photos || [],
        updates: [],
        sync_status: data.sync_status,
        device_id: data.device_id
      }
      
      console.log('✅ Issue created successfully:', issueId)
      return { success: true, data: createdIssue }
    } catch (err) {
      console.error('Exception creating issue:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create issue' 
      }
    }
  }

  /**
   * Update issue status with workflow validation
   * Ensures proper status transitions and audit trail
   */
  async updateIssueStatus(
    issueId: string, 
    newStatus: IssueStatus, 
    notes?: string
  ): Promise<{ success: boolean; data?: IssueReport; error?: string }> {
    try {
      console.log(`Updating issue ${issueId} status to ${newStatus}`)
      
      // Get current issue
      const { data: currentIssue, error: fetchError } = await this.supabase
        .from('reports')
        .select('*')
        .eq('id', issueId)
        .single()
      
      if (fetchError) {
        return { success: false, error: fetchError.message }
      }
      
      // Validate status transition
      const isValidTransition = this.validateStatusTransition(currentIssue.status, newStatus)
      if (!isValidTransition) {
        return { 
          success: false, 
          error: `Invalid status transition from ${currentIssue.status} to ${newStatus}` 
        }
      }
      
      // Update issue
      const { data, error } = await this.supabase
        .from('reports')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', issueId)
        .select()
        .single()
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      // Create update record (would typically be in a separate updates table)
      const updateRecord: IssueUpdate = {
        timestamp: new Date().toISOString(),
        description: notes || `Status changed to ${newStatus}`,
        photos: [],
        previousStatus: currentIssue.status,
        newStatus: newStatus,
        technician: 'Admin User' // Would come from current user context
      }
      
      // Transform to IssueReport format
      const updatedIssue: IssueReport = {
        id: data.id,
        title: data.title,
        category: this.inferCategoryFromContent(data.title, data.description), // Infer since DB doesn't store it
        description: data.description,
        siteId: data.location,
        timestamp: data.created_at,
        severity: data.severity,
        status: data.status,
        photos: [],
        updates: [updateRecord],
        sync_status: data.sync_status,
        device_id: data.device_id
      }
      
      console.log('✅ Issue status updated successfully')
      return { success: true, data: updatedIssue }
    } catch (err) {
      console.error('Exception updating issue status:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update issue status' 
      }
    }
  }

  /**
   * Get issues by site for reporting
   * Returns issues filtered by specific site
   */
  async getIssuesBySite(siteId: string): Promise<{
    success: boolean
    data?: IssueReport[]
    error?: string
  }> {
    try {
      console.log('Fetching issues for site:', siteId)
      
      const { data, error } = await this.supabase
        .from('reports')
        .select('*')
        .eq('location', siteId)
        .order('created_at', { ascending: false })
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      const issues: IssueReport[] = data?.map(this.transformDbRecordToIssue) || []
      
      console.log(`✅ Fetched ${issues.length} issues for site ${siteId}`)
      return { success: true, data: issues }
    } catch (err) {
      console.error('Exception fetching issues by site:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch issues by site' 
      }
    }
  }

  /**
   * Validate issue input data
   * Ensures all required fields are present and valid
   */
  private validateIssueInput(input: IssueReportInput): { isValid: boolean; error?: string } {
    if (!input.title || input.title.trim().length === 0) {
      return { isValid: false, error: 'Title is required' }
    }
    
    if (input.title.length > 200) {
      return { isValid: false, error: 'Title must be less than 200 characters' }
    }
    
    if (!input.siteId || input.siteId.trim().length === 0) {
      return { isValid: false, error: 'Site is required' }
    }
    
    if (!Object.values(IssueSeverity).includes(input.severity)) {
      return { isValid: false, error: 'Invalid severity level' }
    }
    
    if (!Object.values(IssueCategory).includes(input.category)) {
      return { isValid: false, error: 'Invalid category' }
    }
    
    if (input.description && input.description.length > 2000) {
      return { isValid: false, error: 'Description must be less than 2000 characters' }
    }
    
    return { isValid: true }
  }

  /**
   * Validate status transitions
   * Ensures proper workflow is followed
   */
  private validateStatusTransition(currentStatus: IssueStatus, newStatus: IssueStatus): boolean {
    const validTransitions: Record<IssueStatus, IssueStatus[]> = {
      [IssueStatus.New]: [IssueStatus.Assigned, IssueStatus.InProgress],
      [IssueStatus.Assigned]: [IssueStatus.InProgress, IssueStatus.New],
      [IssueStatus.InProgress]: [IssueStatus.Resolved, IssueStatus.Assigned],
      [IssueStatus.Resolved]: [IssueStatus.InProgress] // Can reopen
    }
    
    return validTransitions[currentStatus]?.includes(newStatus) || false
  }

  /**
   * Generate unique issue ID
   */
  private generateIssueId(): string {
    return `issue-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Transform database record to IssueReport format
   */
  private transformDbRecordToIssue(record: any): IssueReport {
    return {
      id: record.id,
      title: record.title,
      category: this.inferCategoryFromContent(record.title, record.description), // Infer since DB doesn't store it
      description: record.description,
      siteId: record.location,
      timestamp: record.created_at,
      severity: record.severity,
      status: record.status,
      photos: [],
      updates: [],
      sync_status: record.sync_status || 'synced',
      device_id: record.device_id
    }
  }

  /**
   * Infer category from content since DB doesn't have category column yet
   * Uses keyword matching to categorize issues
   */
  private inferCategoryFromContent(title: string, description: string): IssueCategory {
    const content = (title + ' ' + (description || '')).toLowerCase()
    
    // Check for audit-related keywords
    if (content.includes('audit') || content.includes('inspection') || content.includes('compliance') || content.includes('review')) {
      return IssueCategory.Audit
    }
    
    // Check for vandalism-related keywords
    if (content.includes('vandalism') || content.includes('graffiti') || content.includes('damage') || content.includes('broken') || content.includes('destroyed')) {
      return IssueCategory.Vandalism
    }
    
    // Check for preventive maintenance keywords
    if (content.includes('maintenance') || content.includes('preventive') || content.includes('scheduled') || content.includes('routine') || content.includes('check')) {
      return IssueCategory.Preventive
    }
    
    // Check for docket-related keywords
    if (content.includes('docket') || content.includes('documentation') || content.includes('record') || content.includes('filing')) {
      return IssueCategory.Docket
    }
    
    // Default to Corrective for repair/fix issues
    return IssueCategory.Corrective
  }
}
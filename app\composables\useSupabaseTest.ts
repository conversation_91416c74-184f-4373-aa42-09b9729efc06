/**
 * Test composable to verify Supabase connectivity
 * This will be used to test the initial connection setup
 */
export const useSupabaseTest = () => {
  const supabase = useSupabaseClient()

  /**
   * Test basic connectivity to Supabase
   * Attempts to fetch sites data to verify connection
   */
  const testConnection = async () => {
    try {
      console.log('Testing Supabase connection...')
      
      // Test basic connectivity by querying sites table
      const { data, error } = await supabase
        .from('sites')
        .select('site_id, site_name')
        .limit(1)
      
      if (error) {
        console.error('Supabase connection error:', error)
        return { success: false, error: error.message }
      }
      
      console.log('Supabase connection successful!')
      console.log('Sample data:', data)
      
      return { success: true, data }
    } catch (err) {
      console.error('Connection test failed:', err)
      return { success: false, error: 'Connection failed' }
    }
  }

  return {
    testConnection
  }
}
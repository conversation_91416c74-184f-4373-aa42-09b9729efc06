// TypeScript interfaces for the OMS Desktop Admin Interface
// Aligned with React Native mobile app data models

/**
 * Photo interface representing attached photos
 */
export interface Photo {
  id: string;
  uri: string;
  timestamp: string;
  title?: string;
}

/**
 * Issue category enumeration
 */
export enum IssueCategory {
  Docket = "Docket",
  Vandalism = "Vandalism",
  Corrective = "Corrective",
  Preventive = "Preventive",
  Audit = "Audit"
}

/**
 * Issue severity enumeration
 */
export enum IssueSeverity {
  Low = "Low",
  Medium = "Medium",
  High = "High"
}

/**
 * Issue status enumeration
 */
export enum IssueStatus {
  New = "New",
  Assigned = "Assigned",
  InProgress = "In Progress",
  Resolved = "Resolved"
}

/**
 * Issue update interface for tracking changes
 */
export interface IssueUpdate {
  timestamp: string;
  description: string;
  photos: Photo[];
  previousStatus?: IssueStatus;
  newStatus: IssueStatus;
  technician?: string;
}

/**
 * Main issue report interface matching mobile app schema
 */
export interface IssueReport {
  id: string;
  title: string;
  category: IssueCategory;
  description: string;
  siteId: string;
  timestamp: string;
  severity: IssueSeverity;
  status: IssueStatus;
  photos: Photo[];
  updates?: IssueUpdate[];
  sync_status: 'pending' | 'synced';
  device_id: string;
}

/**
 * Issue report input type for creating new issues
 */
export type IssueReportInput = Omit<IssueReport, "id" | "sync_status" | "device_id">;

/**
 * Site address interface
 */
export interface SiteAddress {
  street: string;
  latitude: number;
  longitude: number;
  city: string;
  state: string;
  country: string;
}

/**
 * Site status enumeration
 */
export enum SiteStatus {
  Active = "Active",
  Support = "Support",
  NotActive = "Not Active",
  Dismantled = "Dismantled"
}

/**
 * Site interface representing a physical location
 */
export interface Site {
  siteId: string;
  siteName: string;
  siteAddress: SiteAddress;
  status: SiteStatus;
}

/**
 * Site option type for dropdowns
 */
export type SiteOption = {
  value: string;
  label: string;
}

/**
 * Sync status enumeration
 */
export enum SyncStatus {
  Synced = "synced",
  Syncing = "syncing",
  Pending = "pending",
  Error = "error",
  Offline = "offline"
}

/**
 * Sync queue item interface
 */
export interface SyncQueueItem {
  entityType: "issue" | "photo";
  entityId: string;
  operation: "create" | "update" | "delete";
  queuedAt: string;
  attempts: number;
  lastError?: string;
  status: SyncStatus;
}

/**
 * Sync state interface
 */
export interface SyncState {
  isOnline: boolean;
  isSyncing: boolean;
  queueCount: number;
  lastSyncTime?: string;
  isPaused: boolean;
}

// ================================
// DESKTOP ADMIN SPECIFIC TYPES
// ================================

/**
 * User role types (Desktop Admin specific)
 */
export type UserRole = 'Admin' | 'Manager' | 'Technician' | 'User';

/**
 * User interface representing system users (Desktop Admin specific)
 */
export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  phone?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
  profilePicture?: string;
  department?: string;
  siteAssignments: string[]; // Array of site IDs
  skills: string[];
  certifications: string[];
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

/**
 * Activity interface representing system activities and logs (Desktop Admin specific)
 */
export interface Activity {
  id: string;
  userId: string;
  userName: string;
  userRole: UserRole;
  action: string;
  description: string;
  entityType: 'Issue' | 'Site' | 'User' | 'Report';
  entityId: string;
  entityName: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  changes?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  severity: IssueSeverity;
  isSystemGenerated: boolean;
  metadata?: Record<string, any>;
}

/**
 * Dashboard statistics interface (Desktop Admin specific)
 */
export interface DashboardStats {
  totalIssues: number;
  issuesBySeverity: {
    [IssueSeverity.High]: number;
    [IssueSeverity.Medium]: number;
    [IssueSeverity.Low]: number;
  };
  issuesByStatus: {
    [IssueStatus.New]: number;
    [IssueStatus.InProgress]: number;
    [IssueStatus.Resolved]: number;
    [IssueStatus.Assigned]: number;
  };
  totalSites: number;
  activeSites: number;
  totalUsers: number;
  activeUsers: number;
  recentActivities: Activity[];
  criticalIssues: IssueReport[];
  upcomingInspections: Site[];
}

/**
 * Navigation menu item interface (Desktop Admin specific)
 */
export interface NavMenuItem {
  id: string;
  label: string;
  icon: string;
  route: string;
  badge?: number;
  isActive?: boolean;
  children?: NavMenuItem[];
}

// ================================
// SERVICE INTERFACES
// ================================

/**
 * Issue storage service interface
 */
export interface IssueStorageService {
  getAllIssues(): Promise<IssueReport[]>;
  getIssueById(id: string): Promise<IssueReport | null>;
  createIssue(issue: IssueReportInput): Promise<IssueReport>;
  updateIssue(issue: IssueReport): Promise<IssueReport>;
  deleteIssue(id: string): Promise<boolean>;
}

/**
 * File storage service interface
 */
export interface FileStorageService {
  saveImage(uri: string, title?: string): Promise<{ id: string; uri: string }>;
  deleteImage(uri: string): Promise<boolean>;
  getPhotoData(photoId: string): Promise<{ issueId: string; uri: string; title?: string } | null>;
}

/**
 * Validation rule type
 */
export type ValidationRule<T = string> = {
  validator: (value: T) => boolean;
  message: string;
};

/**
 * Field validation result type
 */
export type FieldValidationResult = {
  isValid: boolean;
  errorMessage: string | undefined;
};

// ================================
// CONTEXT TYPES
// ================================

/**
 * Issue context type for state management
 */
export type IssueContextType = {
  issues: IssueReport[];
  loading: boolean;
  error: string | null;
  refreshIssues: () => Promise<void>;
  getIssueById: (id: string) => IssueReport | undefined;
  createIssue: (issueInput: IssueReportInput) => Promise<IssueReport>;
  updateIssue: (updatedIssue: IssueReport) => Promise<IssueReport>;
  deleteIssue: (id: string) => Promise<boolean>;
  addPhotoToIssue: (issueId: string, photo: Photo) => Promise<IssueReport | null>;
  removePhotoFromIssue: (issueId: string, photoId: string) => Promise<IssueReport | null>;
};

/**
 * Sync context type for sync management
 */
export interface SyncContextType {
  syncState: SyncState;
  syncQueue: SyncQueueItem[];
  queueIssueSync: (issueId: string, operation: 'create' | 'update' | 'delete') => Promise<void>;
  queuePhotoSync: (photoId: string, operation: 'create' | 'delete') => Promise<void>;
  syncNow: () => Promise<void>;
  pauseSync: () => void;
  resumeSync: () => void;
  retryFailedItems: () => Promise<void>;
  getIssueStatus: (issueId: string) => SyncStatus;
  clearSyncQueue: () => Promise<void>;
  clearSyncItems: (queueIds: string[]) => Promise<void>;
  updateSyncSettings: (settings: {
    syncInterval?: number;
    networkPreference?: 'all' | 'wifi' | 'cellular';
    batchSize?: number;
    maxRetryAttempts?: number;
  }) => Promise<void>;
  getSyncSettings: () => {
    syncInterval: number;
    networkPreference: 'all' | 'wifi' | 'cellular';
    batchSize: number;
    maxRetryAttempts: number;
  };
}
<template>
  <div class="flex flex-col h-full">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Issues Management</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">
          Manage and track all maintenance issues across your sites
        </p>
      </div>
      <UButton 
        icon="lucide:plus" 
        size="lg" 
        @click="showCreateModal = true"
        class="bg-primary-600 hover:bg-primary-700"
      >
        Create Issue
      </UButton>
    </div>

    <!-- Filters and Search Section -->
    <UCard class="mb-6">
      <div class="space-y-4">
        <!-- Search Bar -->
        <div class="flex gap-4">
          <div class="flex-1">
            <UInput
              v-model="searchQuery"
              icon="lucide:search"
              placeholder="Search issues by title, description, or site..."
              size="lg"
              @input="debouncedSearch"
            />
          </div>
          <UButton 
            variant="outline" 
            @click="clearAllFilters"
            :disabled="!hasActiveFilters"
          >
            Clear Filters
          </UButton>
        </div>

        <!-- Filter Controls -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Severity Filter -->
          <USelectMenu
            v-model="selectedSeverity"
            :items="severityOptions"
            value-key="value"
            placeholder="Filter by Severity"
            searchable
            clear-search-on-close
          >
            <template #leading>
              <span v-if="selectedSeverity" class="flex items-center gap-2">
                <UBadge :color="getSeverityColor(selectedSeverity)" size="xs">
                  {{ selectedSeverity }}
                </UBadge>
              </span>
              <span v-else>All Severities</span>
            </template>
          </USelectMenu>

          <!-- Status Filter -->
          <USelectMenu
            v-model="selectedStatus"
            :items="statusOptions"
            value-key="value"
            placeholder="Filter by Status"
            searchable
            clear-search-on-close
          >
            <template #leading>
              <span v-if="selectedStatus" class="flex items-center gap-2">
                <UBadge :color="getStatusColor(selectedStatus)" size="xs">
                  {{ selectedStatus }}
                </UBadge>
              </span>
              <span v-else>All Statuses</span>
            </template>
          </USelectMenu>

          <!-- Site Filter -->
          <USelectMenu
            v-model="selectedSite"
            :items="siteOptions"
            value-key="value"
            placeholder="Filter by Site"
            searchable
            clear-search-on-close
          >
            <template #leading>
              <span v-if="selectedSite">{{ getSiteName(selectedSite) }}</span>
              <span v-else>All Sites</span>
            </template>
          </USelectMenu>

          <!-- Category Filter -->
          <USelectMenu
            v-model="selectedCategory"
            :items="categoryOptions"
            value-key="value"
            placeholder="Filter by Category"
            searchable
            clear-search-on-close
          >
            <template #leading>
              <span v-if="selectedCategory">{{ selectedCategory }}</span>
              <span v-else>All Categories</span>
            </template>
          </USelectMenu>
        </div>
      </div>
    </UCard>

    <!-- Issues Table -->
    <UCard class="flex-1">
      <!-- Table Header with Stats and Bulk Actions -->
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Issues ({{ filteredIssues.length }})
            </h3>
            <div class="flex gap-2">
              <UBadge color="error" size="sm">{{ highSeverityCount }} High</UBadge>
              <UBadge color="warning" size="sm">{{ mediumSeverityCount }} Medium</UBadge>
              <UBadge color="success" size="sm">{{ lowSeverityCount }} Low</UBadge>
            </div>
          </div>
          
          <!-- Bulk Actions -->
          <div v-if="selectedIssues.length > 0" class="flex items-center gap-2">
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ selectedIssues.length }} selected
            </span>
            <UDropdownMenu :items="bulkActionItems">
              <UButton variant="outline" size="sm" icon="lucide:more-horizontal">
                Bulk Actions
              </UButton>
            </UDropdownMenu>
          </div>
        </div>
      </template>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="text-center">
          <UIcon name="lucide:loader-2" class="w-8 h-8 animate-spin mx-auto mb-2 text-primary-600" />
          <p class="text-gray-600 dark:text-gray-400">Loading issues...</p>
        </div>
      </div>

      <!-- Issues Table -->
      <UTable
        v-else
        :data="paginatedIssues"
        :columns="tableColumns"
        :loading="isLoading"
        :empty="'No issues found'"
        class="w-full"
      >
        <!-- Multi-select Column -->
        <template #checkbox-data="{ row }">
          <UCheckbox
            :model-value="selectedIssues.includes((row as unknown as IssueReport).id)"
            @update:model-value="(value: boolean | 'indeterminate') => toggleIssueSelection((row as unknown as IssueReport).id, value === true)"
            class="flex justify-center"
          />
        </template>

        <!-- Title Column with Link -->
        <template #title-data="{ row }">
          <div class="max-w-xs">
            <NuxtLink
              :to="`/issues/${(row as unknown as IssueReport).id}`"
              class="font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300"
            >
#
            </NuxtLink>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
              {{ (row as unknown as IssueReport).description }}
            </p>
          </div>
        </template>

        <!-- Site Column -->
        <template #site-data="{ row }">
          <div class="text-sm">
            <p class="font-medium text-gray-900 dark:text-gray-100">
              {{ getSiteName((row as unknown as IssueReport).siteId) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              ID: {{ (row as unknown as IssueReport).siteId }}
            </p>
          </div>
        </template>

        <!-- Severity Column -->
        <template #severity-data="{ row }">
          <UBadge :color="getSeverityColor((row as unknown as IssueReport).severity)" size="sm">
            {{ (row as unknown as IssueReport).severity }}
          </UBadge>
        </template>

        <!-- Status Column -->
        <template #status-data="{ row }">
          <UBadge :color="getStatusColor((row as unknown as IssueReport).status)" size="sm">
            {{ (row as unknown as IssueReport).status }}
          </UBadge>
        </template>

        <!-- Category Column -->
        <template #category-data="{ row }">
          <span class="text-sm text-gray-600 dark:text-gray-400">
            {{ (row as unknown as IssueReport).category }}
          </span>
        </template>

        <!-- Created Date Column -->
        <template #created-data="{ row }">
          <div class="text-sm">
            <p class="text-gray-900 dark:text-gray-100">
              {{ formatDate((row as unknown as IssueReport).timestamp) }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatTime((row as unknown as IssueReport).timestamp) }}
            </p>
          </div>
        </template>

        <!-- Actions Column -->
        <template #actions-data="{ row }">
          <UDropdownMenu :items="getRowActionItems(row as unknown as IssueReport)">
            <UButton
              variant="ghost"
              icon="lucide:more-horizontal"
              size="sm"
              class="hover:bg-gray-100 dark:hover:bg-gray-800"
            />
          </UDropdownMenu>
        </template>
      </UTable>

      <!-- Pagination -->
      <template #footer>
        <div class="flex justify-between items-center px-3 py-3.5 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-700 dark:text-gray-300">
              Showing {{ ((currentPage - 1) * pageSize) + 1 }} to {{ Math.min(currentPage * pageSize, filteredIssues.length) }} of {{ filteredIssues.length }} issues
            </span>
          </div>
          <UPagination 
            v-model="currentPage" 
            :page-count="pageSize" 
            :total="filteredIssues.length"
            size="sm"
          />
        </div>
      </template>
    </UCard>

    <!-- Create Issue Modal (Placeholder) -->
    <UModal v-model="showCreateModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Create New Issue</h3>
        </template>
        
        <div class="p-6 text-center">
          <UIcon name="lucide:construction" class="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-600 dark:text-gray-400">Issue creation form will be implemented next.</p>
        </div>
        
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton variant="ghost" @click="showCreateModal = false">Cancel</UButton>
            <UButton @click="showCreateModal = false">Create Issue</UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { mockIssueReports, mockSites } from '~/data/mockData'
import type { IssueReport, IssueSeverity, IssueStatus, IssueCategory } from '~/types'
import { IssueCategory as IssueCategoryEnum, IssueSeverity as IssueSeverityEnum, IssueStatus as IssueStatusEnum } from '~/types'
import type { TableColumn } from '@nuxt/ui'
import { useDebounceFn } from '@vueuse/core'

// Set page metadata using layout functions
const setPageTitle = inject('setPageTitle') as (title: string) => void
const setPageDescription = inject('setPageDescription') as (description: string) => void
const setBreadcrumbs = inject('setBreadcrumbs') as (crumbs: Array<{ label: string, to?: string }>) => void

// Set page information
setPageTitle('Issues Management')
setPageDescription('Manage and track all maintenance issues across your sites')
setBreadcrumbs([
  { label: 'Issues Management' }
])

// ================================
// STATE MANAGEMENT
// ================================

// Issues data (using mock data for now)
const allIssues = ref<IssueReport[]>(mockIssueReports)
const isLoading = ref(false)

// Search and filter state
const searchQuery = ref('')
const selectedSeverity = ref<IssueSeverity | undefined>(undefined)
const selectedStatus = ref<IssueStatus | undefined>(undefined)
const selectedCategory = ref<IssueCategory | undefined>(undefined)
const selectedSite = ref<string | undefined>(undefined)

// Selection and bulk actions
const selectedIssues = ref<string[]>([])
const showCreateModal = ref(false)

/**
 * Toggle issue selection for multi-select functionality
 */
const toggleIssueSelection = (issueId: string, checked: boolean) => {
  if (checked) {
    if (!selectedIssues.value.includes(issueId)) {
      selectedIssues.value.push(issueId)
    }
  } else {
    const index = selectedIssues.value.indexOf(issueId)
    if (index > -1) {
      selectedIssues.value.splice(index, 1)
    }
  }
  console.log('🔄 Selected issues:', selectedIssues.value)
}

// Pagination
const currentPage = ref(1)
const pageSize = ref(25)

// ================================
// COMPUTED PROPERTIES
// ================================

/**
 * Filter options for dropdowns
 */
const severityOptions = computed(() => 
  Object.values(IssueSeverityEnum).map(severity => ({
    label: severity,
    value: severity
  }))
)

const statusOptions = computed(() => 
  Object.values(IssueStatusEnum).map(status => ({
    label: status,
    value: status
  }))
)

const categoryOptions = computed(() => 
  Object.values(IssueCategoryEnum).map(category => ({
    label: category,
    value: category
  }))
)

const siteOptions = computed(() => 
  mockSites.map(site => ({
    label: site.siteName,
    value: site.siteId
  }))
)

/**
 * Filtered issues based on search and filter criteria
 */
const filteredIssues = computed(() => {
  let filtered = [...allIssues.value]

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(issue => 
      issue.title.toLowerCase().includes(query) ||
      issue.description.toLowerCase().includes(query) ||
      getSiteName(issue.siteId).toLowerCase().includes(query)
    )
  }

  // Apply severity filter
  if (selectedSeverity.value) {
    filtered = filtered.filter(issue => issue.severity === selectedSeverity.value)
  }

  // Apply status filter
  if (selectedStatus.value) {
    filtered = filtered.filter(issue => issue.status === selectedStatus.value)
  }

  // Apply category filter
  if (selectedCategory.value) {
    filtered = filtered.filter(issue => issue.category === selectedCategory.value)
  }

  // Apply site filter
  if (selectedSite.value) {
    filtered = filtered.filter(issue => issue.siteId === selectedSite.value)
  }

  return filtered
})

/**
 * Paginated issues for current page
 */
const paginatedIssues = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return filteredIssues.value.slice(startIndex, endIndex)
})

/**
 * Statistics for severity counts
 */
const highSeverityCount = computed(() => 
  filteredIssues.value.filter(issue => issue.severity === 'High').length
)

const mediumSeverityCount = computed(() => 
  filteredIssues.value.filter(issue => issue.severity === 'Medium').length
)

const lowSeverityCount = computed(() => 
  filteredIssues.value.filter(issue => issue.severity === 'Low').length
)

/**
 * Check if any filters are active
 */
const hasActiveFilters = computed(() =>
  searchQuery.value.trim() !== '' ||
  selectedSeverity.value !== undefined ||
  selectedStatus.value !== undefined ||
  selectedCategory.value !== undefined ||
  selectedSite.value !== undefined
)

// ================================
// TABLE CONFIGURATION
// ================================

/**
 * Table columns configuration
 */
const tableColumns: TableColumn<IssueReport>[] = [
  {
    id: 'checkbox',
    header: '',
    enableSorting: false,
    meta: {
      class: {
        td: 'w-12',
        th: 'w-12'
      }
    }
  },
  {
    accessorKey: 'title',
    header: 'Issue',
    enableSorting: true,
    meta: {
      class: {
        td: 'min-w-64',
        th: 'min-w-64'
      }
    }
  },
  {
    accessorKey: 'siteId',
    id: 'site',
    header: 'Site',
    enableSorting: true,
    meta: {
      class: {
        td: 'w-40',
        th: 'w-40'
      }
    }
  },
  {
    accessorKey: 'severity',
    header: 'Severity',
    enableSorting: true,
    meta: {
      class: {
        td: 'w-24',
        th: 'w-24'
      }
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    enableSorting: true,
    meta: {
      class: {
        td: 'w-32',
        th: 'w-32'
      }
    }
  },
  {
    accessorKey: 'category',
    header: 'Category',
    enableSorting: true,
    meta: {
      class: {
        td: 'w-32',
        th: 'w-32'
      }
    }
  },
  {
    accessorKey: 'timestamp',
    id: 'created',
    header: 'Created',
    enableSorting: true,
    meta: {
      class: {
        td: 'w-32',
        th: 'w-32'
      }
    }
  },
  {
    id: 'actions',
    header: '',
    enableSorting: false,
    meta: {
      class: {
        td: 'w-16',
        th: 'w-16'
      }
    }
  }
]

/**
 * Bulk action menu items
 */
const bulkActionItems = [
  [{
    label: 'Assign to Technician',
    icon: 'lucide:user-plus',
    click: () => handleBulkAssign()
  }],
  [{
    label: 'Update Status',
    icon: 'lucide:refresh-cw',
    click: () => handleBulkStatusUpdate()
  }],
  [{
    label: 'Export Selected',
    icon: 'lucide:download',
    click: () => handleBulkExport()
  }],
  [{
    label: 'Delete Selected',
    icon: 'lucide:trash-2',
    click: () => handleBulkDelete()
  }]
]

// ================================
// UTILITY FUNCTIONS
// ================================

/**
 * Get site name by ID
 */
const getSiteName = (siteId: string): string => {
  const site = mockSites.find(s => s.siteId === siteId)
  return site ? site.siteName : `Site ${siteId}`
}

/**
 * Get color for severity badge - must match UBadge color prop types
 */
const getSeverityColor = (severity: IssueSeverity): 'error' | 'warning' | 'success' | 'neutral' => {
  switch (severity) {
    case 'High': return 'error'
    case 'Medium': return 'warning'
    case 'Low': return 'success'
    default: return 'neutral'
  }
}

/**
 * Get color for status badge - must match UBadge color prop types
 */
const getStatusColor = (status: IssueStatus): 'info' | 'secondary' | 'warning' | 'success' | 'neutral' => {
  switch (status) {
    case 'New': return 'info'
    case 'Assigned': return 'secondary'
    case 'In Progress': return 'warning'
    case 'Resolved': return 'success'
    default: return 'neutral'
  }
}

/**
 * Format date for display
 */
const formatDate = (timestamp: string): string => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

/**
 * Format time for display
 */
const formatTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * Get row action menu items for each issue
 */
const getRowActionItems = (issue: IssueReport) => [
  [{
    label: 'View Details',
    icon: 'lucide:eye',
    click: () => navigateTo(`/issues/${issue.id}`)
  }],
  [{
    label: 'Edit Issue',
    icon: 'lucide:edit',
    click: () => handleEditIssue(issue.id)
  }],
  [{
    label: 'Assign Technician',
    icon: 'lucide:user-plus',
    click: () => handleAssignTechnician(issue.id)
  }],
  [{
    label: 'Update Status',
    icon: 'lucide:refresh-cw',
    click: () => handleUpdateStatus(issue.id)
  }],
  [{
    label: 'Delete Issue',
    icon: 'lucide:trash-2',
    click: () => handleDeleteIssue(issue.id)
  }]
]

// ================================
// EVENT HANDLERS
// ================================

/**
 * Debounced search function
 */
const debouncedSearch = useDebounceFn(() => {
  console.log('🔍 Searching for:', searchQuery.value)
  currentPage.value = 1 // Reset to first page when searching
}, 300)

/**
 * Clear all active filters
 */
const clearAllFilters = () => {
  console.log('🧹 Clearing all filters')
  searchQuery.value = ''
  selectedSeverity.value = undefined
  selectedStatus.value = undefined
  selectedCategory.value = undefined
  selectedSite.value = undefined
  currentPage.value = 1
}

/**
 * Handle individual issue actions (placeholders)
 */
const handleEditIssue = (issueId: string) => {
  console.log('✏️ Edit issue:', issueId)
  // TODO: Implement edit functionality
}

const handleAssignTechnician = (issueId: string) => {
  console.log('👤 Assign technician to issue:', issueId)
  // TODO: Implement assign functionality
}

const handleUpdateStatus = (issueId: string) => {
  console.log('🔄 Update status for issue:', issueId)
  // TODO: Implement status update functionality
}

const handleDeleteIssue = (issueId: string) => {
  console.log('🗑️ Delete issue:', issueId)
  // TODO: Implement delete functionality with confirmation
}

/**
 * Handle bulk actions (placeholders)
 */
const handleBulkAssign = () => {
  console.log('👥 Bulk assign selected issues:', selectedIssues.value)
  // TODO: Implement bulk assign functionality
}

const handleBulkStatusUpdate = () => {
  console.log('🔄 Bulk status update for issues:', selectedIssues.value)
  // TODO: Implement bulk status update functionality
}

const handleBulkExport = () => {
  console.log('📥 Export selected issues:', selectedIssues.value)
  // TODO: Implement export functionality
}

const handleBulkDelete = () => {
  console.log('🗑️ Bulk delete selected issues:', selectedIssues.value)
  // TODO: Implement bulk delete functionality with confirmation
}

// ================================
// LIFECYCLE HOOKS
// ================================

/**
 * Initialize page data on mount
 */
onMounted(() => {
  console.log('📋 Issues page mounted - loaded', allIssues.value.length, 'issues')
  console.log('🏢 Available sites:', mockSites.length)
})

/**
 * Watch for filter changes to reset pagination
 */
watch([selectedSeverity, selectedStatus, selectedCategory, selectedSite], () => {
  currentPage.value = 1
})
</script>
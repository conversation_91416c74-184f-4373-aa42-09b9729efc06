/**
 * Issues data management composable with CRUD operations
 * Handles all issue-related operations for the desktop admin interface
 * Provides real-time updates and comprehensive issue management
 */

import type { RealtimeChannel } from '@supabase/supabase-js'
import type { 
  IssueReport, 
  IssueReportInput, 
  Photo,
  Site
} from '~/types'
import { 
  IssueCategory, 
  IssueSeverity, 
  IssueStatus
} from '~/types'
import type { AdminQueryOptions } from '~/types/supabase'

export const useIssues = () => {
  // Get Supabase client and authentication
  const { supabase, executeAdminQuery, setupRealtimeSubscription } = useSupabase()
  const { hasPermission } = useAuth()
  
  // Reactive state for issues management
  const issues = ref<IssueReport[]>([])
  const isLoading = ref(false)
  const isCreating = ref(false)
  const isUpdating = ref(false)
  const error = ref<string | null>(null)
  const totalCount = ref(0)
  const realtimeChannel = ref<RealtimeChannel | null>(null)
  
  // Filters and search state
  const filters = ref({
    severity: null as IssueSeverity | null,
    status: null as IssueStatus | null,
    category: null as IssueCategory | null,
    siteId: null as string | null,
    search: '',
    dateFrom: null as string | null,
    dateTo: null as string | null
  })
  
  // Pagination state
  const pagination = ref({
    page: 1,
    pageSize: 25,
    orderBy: 'created_at' as keyof IssueReport,
    ascending: false
  })

  /**
   * Fetch all issues with admin access and filtering
   * Returns issues from all devices for comprehensive management
   */
  const fetchIssues = async (refreshCache = false): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null
      
      console.log('Fetching issues with filters:', filters.value)
      
      const options: AdminQueryOptions = {
        includeAllDevices: true,
        orderBy: pagination.value.orderBy as any,
        ascending: pagination.value.ascending,
        limit: pagination.value.pageSize,
        offset: (pagination.value.page - 1) * pagination.value.pageSize
      }
      
      const result = await executeAdminQuery(async (client) => {
        let query = client
          .from('reports')
          .select('*', { count: 'exact' })
        
        // Apply filters
        if (filters.value.severity) {
          query = query.eq('severity', filters.value.severity)
        }
        
        if (filters.value.status) {
          query = query.eq('status', filters.value.status)
        }
        
        if (filters.value.category) {
          query = query.eq('category', filters.value.category)
        }
        
        if (filters.value.siteId) {
          query = query.eq('location', filters.value.siteId) // location maps to siteId
        }
        
        if (filters.value.search) {
          query = query.or(`title.ilike.%${filters.value.search}%,description.ilike.%${filters.value.search}%`)
        }
        
        if (filters.value.dateFrom) {
          query = query.gte('created_at', filters.value.dateFrom)
        }
        
        if (filters.value.dateTo) {
          query = query.lte('created_at', filters.value.dateTo)
        }
        
        // Apply ordering
        query = query.order(options.orderBy || 'created_at', { ascending: options.ascending })
        
        // Apply pagination
        if (options.limit) {
          query = query.limit(options.limit)
        }
        
        if (options.offset) {
          query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
        }
        
        return await query
      })
      
      if (result.success && result.data) {
        // Transform database records to IssueReport format
        issues.value = transformDbRecordsToIssueReports(result.data.data || [])
        totalCount.value = result.data.count || 0
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`✅ Fetched ${issues.value.length} issues (${totalCount.value} total)`)
        }
      } else {
        console.error('Failed to fetch issues:', result.error)
        error.value = result.error || 'Failed to fetch issues'
      }
    } catch (err) {
      console.error('Exception while fetching issues:', err)
      error.value = err instanceof Error ? err.message : 'Failed to fetch issues'
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Create a new issue
   * Adds proper admin metadata and device tracking
   */
  const createIssue = async (issueInput: IssueReportInput): Promise<{ success: boolean; data?: IssueReport; error?: string }> => {
    try {
      if (!hasPermission('issues.create')) {
        return { success: false, error: 'Permission denied: Cannot create issues' }
      }
      
      isCreating.value = true
      error.value = null
      
      console.log('Creating new issue:', issueInput.title)
      
      const newIssue = {
        id: generateUUID(),
        device_id: 'desktop-admin',
        title: issueInput.title,
        description: issueInput.description,
        location: issueInput.siteId, // Maps to siteId
        severity: issueInput.severity,
        status: issueInput.status,
        // category: issueInput.category, // Column doesn't exist in current schema
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sync_status: 'synced'
      }
      
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('reports')
          .insert([newIssue])
          .select()
          .single()
      })
      
      if (result.success && result.data) {
        const createdIssue = transformDbRecordToIssueReport(result.data)
        // Override category with input since DB doesn't store it yet
        createdIssue.category = issueInput.category
        issues.value.unshift(createdIssue)
        totalCount.value += 1
        
        console.log('✅ Issue created successfully:', createdIssue.id)
        return { success: true, data: createdIssue }
      } else {
        console.error('Failed to create issue:', result.error)
        return { success: false, error: result.error || 'Failed to create issue' }
      }
    } catch (err) {
      console.error('Exception while creating issue:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to create issue'
      return { success: false, error: errorMessage }
    } finally {
      isCreating.value = false
    }
  }

  /**
   * Update an existing issue
   * Handles status changes and metadata updates
   */
  const updateIssue = async (issueId: string, updates: Partial<IssueReport>): Promise<{ success: boolean; data?: IssueReport; error?: string }> => {
    try {
      if (!hasPermission('issues.update')) {
        return { success: false, error: 'Permission denied: Cannot update issues' }
      }
      
      isUpdating.value = true
      error.value = null
      
      console.log('Updating issue:', issueId)
      
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      }
      
      // Remove fields that shouldn't be updated directly
      delete updateData.id
      delete updateData.device_id
      delete updateData.timestamp
      delete updateData.photos
      delete updateData.updates
      
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('reports')
          .update(updateData)
          .eq('id', issueId)
          .select()
          .single()
      })
      
      if (result.success && result.data) {
        const updatedIssue = transformDbRecordToIssueReport(result.data)
        
        // Update local state
        const index = issues.value.findIndex(issue => issue.id === issueId)
        if (index !== -1) {
          issues.value[index] = updatedIssue
        }
        
        console.log('✅ Issue updated successfully:', issueId)
        return { success: true, data: updatedIssue }
      } else {
        console.error('Failed to update issue:', result.error)
        return { success: false, error: result.error || 'Failed to update issue' }
      }
    } catch (err) {
      console.error('Exception while updating issue:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update issue'
      return { success: false, error: errorMessage }
    } finally {
      isUpdating.value = false
    }
  }

  /**
   * Delete an issue
   * Removes issue and associated photos
   */
  const deleteIssue = async (issueId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!hasPermission('issues.delete')) {
        return { success: false, error: 'Permission denied: Cannot delete issues' }
      }
      
      console.log('Deleting issue:', issueId)
      
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('reports')
          .delete()
          .eq('id', issueId)
      })
      
      if (result.success) {
        // Remove from local state
        issues.value = issues.value.filter(issue => issue.id !== issueId)
        totalCount.value -= 1
        
        console.log('✅ Issue deleted successfully:', issueId)
        return { success: true }
      } else {
        console.error('Failed to delete issue:', result.error)
        return { success: false, error: result.error || 'Failed to delete issue' }
      }
    } catch (err) {
      console.error('Exception while deleting issue:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete issue'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Get issue by ID with full details
   */
  const getIssueById = async (issueId: string): Promise<IssueReport | null> => {
    try {
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('reports')
          .select('*')
          .eq('id', issueId)
          .single()
      })
      
      if (result.success && result.data) {
        return transformDbRecordToIssueReport(result.data)
      }
      
      return null
    } catch (err) {
      console.error('Error fetching issue by ID:', err)
      return null
    }
  }

  /**
   * Apply filters and refresh issues
   */
  const applyFilters = async (newFilters: typeof filters.value) => {
    filters.value = { ...newFilters }
    pagination.value.page = 1 // Reset to first page
    await fetchIssues()
  }

  /**
   * Clear all filters
   */
  const clearFilters = async () => {
    filters.value = {
      severity: null,
      status: null,
      category: null,
      siteId: null,
      search: '',
      dateFrom: null,
      dateTo: null
    }
    pagination.value.page = 1
    await fetchIssues()
  }

  /**
   * Setup real-time updates for issues
   */
  const setupRealtimeUpdates = () => {
    if (realtimeChannel.value) {
      console.log('Real-time channel already active')
      return
    }
    
    const channel = setupRealtimeSubscription('reports', (payload) => {
      console.log('Real-time issue update:', payload)
      
      if (payload.eventType === 'INSERT') {
        const newIssue = transformDbRecordToIssueReport(payload.new)
        issues.value.unshift(newIssue)
        totalCount.value += 1
      } else if (payload.eventType === 'UPDATE') {
        const updatedIssue = transformDbRecordToIssueReport(payload.new)
        const index = issues.value.findIndex(issue => issue.id === updatedIssue.id)
        if (index !== -1) {
          issues.value[index] = updatedIssue
        }
      } else if (payload.eventType === 'DELETE') {
        issues.value = issues.value.filter(issue => issue.id !== payload.old.id)
        totalCount.value -= 1
      }
    })
    
    realtimeChannel.value = channel // Can be null if subscription failed
  }

  /**
   * Transform database record to IssueReport format
   */
  const transformDbRecordToIssueReport = (record: any): IssueReport => {
    return {
      id: record.id,
      title: record.title,
      category: inferCategoryFromContent(record.title, record.description), // Infer since DB doesn't store it
      description: record.description,
      siteId: record.location, // Maps from location field
      timestamp: record.created_at,
      severity: record.severity,
      status: record.status,
      photos: [], // Photos will be loaded separately
      updates: [], // Updates will be loaded separately
      sync_status: record.sync_status || 'synced',
      device_id: record.device_id
    }
  }

  /**
   * Transform multiple database records
   */
  const transformDbRecordsToIssueReports = (records: any[]): IssueReport[] => {
    return records.map(transformDbRecordToIssueReport)
  }

  /**
   * Generate UUID for new issues
   */
  const generateUUID = (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * Infer category from content since DB doesn't have category column yet
   * Uses keyword matching to categorize issues
   */
  const inferCategoryFromContent = (title: string, description: string): IssueCategory => {
    const content = (title + ' ' + (description || '')).toLowerCase()
    
    // Check for audit-related keywords
    if (content.includes('audit') || content.includes('inspection') || content.includes('compliance') || content.includes('review')) {
      return IssueCategory.Audit
    }
    
    // Check for vandalism-related keywords
    if (content.includes('vandalism') || content.includes('graffiti') || content.includes('damage') || content.includes('broken') || content.includes('destroyed')) {
      return IssueCategory.Vandalism
    }
    
    // Check for preventive maintenance keywords
    if (content.includes('maintenance') || content.includes('preventive') || content.includes('scheduled') || content.includes('routine') || content.includes('check')) {
      return IssueCategory.Preventive
    }
    
    // Check for docket-related keywords
    if (content.includes('docket') || content.includes('documentation') || content.includes('record') || content.includes('filing')) {
      return IssueCategory.Docket
    }
    
    // Default to Corrective for repair/fix issues
    return IssueCategory.Corrective
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    if (realtimeChannel.value) {
      try {
        realtimeChannel.value.unsubscribe()
      } catch (error) {
        console.warn('Error unsubscribing from real-time channel:', error)
      }
      realtimeChannel.value = null
    }
  }

  // Initialize on mount
  onMounted(async () => {
    await fetchIssues()
    setupRealtimeUpdates()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    issues: readonly(issues),
    isLoading: readonly(isLoading),
    isCreating: readonly(isCreating),
    isUpdating: readonly(isUpdating),
    error: readonly(error),
    totalCount: readonly(totalCount),
    filters: readonly(filters),
    pagination: readonly(pagination),
    
    // Actions
    fetchIssues,
    createIssue,
    updateIssue,
    deleteIssue,
    getIssueById,
    
    // Filtering
    applyFilters,
    clearFilters,
    
    // Real-time
    setupRealtimeUpdates,
    cleanup
  }
}
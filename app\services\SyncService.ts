/**
 * Sync monitoring and management service class
 * Handles business logic for sync operations and monitoring
 * Provides comprehensive sync management for the desktop admin interface
 */

import type { SyncMonitoring } from '~/types/supabase'

export class SyncService {
  private supabase: any
  
  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  /**
   * Get comprehensive sync monitoring data for admin dashboard
   * Returns sync status across all devices and entities
   */
  async getSyncMonitoring(): Promise<{
    success: boolean
    data?: SyncMonitoring
    error?: string
  }> {
    try {
      console.log('Fetching sync monitoring data...')
      
      // Get total reports count
      const { count: totalReports, error: totalError } = await this.supabase
        .from('reports')
        .select('*', { count: 'exact', head: true })
      
      if (totalError) {
        return { success: false, error: totalError.message }
      }
      
      // Get synced reports count
      const { count: syncedReports, error: syncedError } = await this.supabase
        .from('reports')
        .select('*', { count: 'exact', head: true })
        .eq('sync_status', 'synced')
      
      if (syncedError) {
        return { success: false, error: syncedError.message }
      }
      
      // Get pending reports count
      const { count: pendingReports, error: pendingError } = await this.supabase
        .from('reports')
        .select('*', { count: 'exact', head: true })
        .eq('sync_status', 'pending')
      
      if (pendingError) {
        return { success: false, error: pendingError.message }
      }
      
      // Get latest sync logs
      const { data: syncLogs, error: syncLogsError } = await this.supabase
        .from('sync_logs')
        .select('*')
        .order('sync_timestamp', { ascending: false })
        .limit(100)
      
      if (syncLogsError) {
        console.warn('Could not fetch sync logs:', syncLogsError)
      }
      
      // Get active devices
      const { data: activeDevicesData, error: devicesError } = await this.supabase
        .from('reports')
        .select('device_id')
        .gte('updated_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      
      const activeDevices = activeDevicesData 
        ? [...new Set(activeDevicesData.map((r: any) => r.device_id))]
        : []
      
      // Calculate last sync time
      const lastSyncTime = syncLogs && syncLogs.length > 0 
        ? syncLogs[0].sync_timestamp 
        : new Date().toISOString()
      
      console.log('✅ Sync monitoring data fetched successfully')
      
      return {
        success: true,
        data: {
          totalReports: totalReports || 0,
          syncedReports: syncedReports || 0,
          pendingReports: pendingReports || 0,
          lastSyncTime,
          activeDevices,
          syncErrors: [] // Would be populated from error logs
        }
      }
    } catch (err) {
      console.error('Error fetching sync monitoring data:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch sync monitoring data' 
      }
    }
  }

  /**
   * Get sync statistics by device
   * Returns device-specific sync performance data
   */
  async getSyncStatsByDevice(): Promise<{
    success: boolean
    data?: Array<{
      deviceId: string
      totalReports: number
      syncedReports: number
      pendingReports: number
      lastSyncTime: string
      syncSuccessRate: number
      averageSyncTime: number
    }>
    error?: string
  }> {
    try {
      console.log('Fetching sync statistics by device...')
      
      // Get all devices with their reports
      const { data: allReports, error: reportsError } = await this.supabase
        .from('reports')
        .select('device_id, sync_status, created_at, updated_at')
      
      if (reportsError) {
        return { success: false, error: reportsError.message }
      }
      
      // Get sync logs for timing analysis
      const { data: syncLogs, error: logsError } = await this.supabase
        .from('sync_logs')
        .select('device_id, sync_timestamp, reports_synced')
        .order('sync_timestamp', { ascending: false })
      
      if (logsError) {
        console.warn('Could not fetch sync logs for timing analysis:', logsError)
      }
      
      // Group reports by device
      const deviceStats = new Map()
      
      allReports?.forEach((report: any) => {
        const deviceId = report.device_id
        
        if (!deviceStats.has(deviceId)) {
          deviceStats.set(deviceId, {
            deviceId,
            totalReports: 0,
            syncedReports: 0,
            pendingReports: 0,
            lastSyncTime: report.updated_at,
            syncSuccessRate: 0,
            averageSyncTime: 0
          })
        }
        
        const stats = deviceStats.get(deviceId)
        stats.totalReports++
        
        if (report.sync_status === 'synced') {
          stats.syncedReports++
        } else if (report.sync_status === 'pending') {
          stats.pendingReports++
        }
        
        // Update last sync time if this report is newer
        if (report.updated_at > stats.lastSyncTime) {
          stats.lastSyncTime = report.updated_at
        }
      })
      
      // Calculate success rates and timing
      deviceStats.forEach((stats, deviceId) => {
        stats.syncSuccessRate = stats.totalReports > 0 
          ? Math.round((stats.syncedReports / stats.totalReports) * 100) 
          : 0
        
        // Calculate average sync time from logs
        const deviceLogs = syncLogs?.filter((log: any) => log.device_id === deviceId) || []
        if (deviceLogs.length > 1) {
          const syncTimes = deviceLogs.map((log: any, index: number) => {
            if (index === deviceLogs.length - 1) return 0 // Skip last one
            const current = new Date(log.sync_timestamp)
            const next = new Date(deviceLogs[index + 1].sync_timestamp)
            return current.getTime() - next.getTime()
          }).filter(time => time > 0)
          
          stats.averageSyncTime = syncTimes.length > 0
            ? Math.round(syncTimes.reduce((a, b) => a + b, 0) / syncTimes.length / 1000) // Convert to seconds
            : 0
        }
      })
      
      const result = Array.from(deviceStats.values())
      console.log(`✅ Sync statistics fetched for ${result.length} devices`)
      
      return { success: true, data: result }
    } catch (err) {
      console.error('Error fetching sync statistics by device:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch sync statistics by device' 
      }
    }
  }

  /**
   * Force sync for specific device
   * Triggers manual sync process for troubleshooting
   */
  async forceSyncForDevice(deviceId: string): Promise<{
    success: boolean
    data?: { syncedCount: number; message: string }
    error?: string
  }> {
    try {
      console.log('Forcing sync for device:', deviceId)
      
      // Get pending reports for this device
      const { data: pendingReports, error: pendingError } = await this.supabase
        .from('reports')
        .select('id')
        .eq('device_id', deviceId)
        .eq('sync_status', 'pending')
      
      if (pendingError) {
        return { success: false, error: pendingError.message }
      }
      
      if (!pendingReports || pendingReports.length === 0) {
        return { 
          success: true, 
          data: { 
            syncedCount: 0, 
            message: 'No pending reports found for this device' 
          } 
        }
      }
      
      // Update all pending reports to synced
      const { error: updateError } = await this.supabase
        .from('reports')
        .update({ 
          sync_status: 'synced',
          updated_at: new Date().toISOString()
        })
        .eq('device_id', deviceId)
        .eq('sync_status', 'pending')
      
      if (updateError) {
        return { success: false, error: updateError.message }
      }
      
      // Create sync log entry
      await this.createSyncLogEntry(deviceId, pendingReports.length)
      
      console.log(`✅ Force sync completed for device ${deviceId}: ${pendingReports.length} reports`)
      
      return { 
        success: true, 
        data: { 
          syncedCount: pendingReports.length,
          message: `Successfully synced ${pendingReports.length} reports`
        } 
      }
    } catch (err) {
      console.error('Error forcing sync for device:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to force sync for device' 
      }
    }
  }

  /**
   * Get sync health dashboard
   * Returns overall system sync health metrics
   */
  async getSyncHealth(): Promise<{
    success: boolean
    data?: {
      overallHealth: 'healthy' | 'warning' | 'critical'
      syncSuccessRate: number
      averageSyncDelay: number
      problemDevices: string[]
      recentSyncTrends: Array<{
        date: string
        syncedCount: number
        pendingCount: number
      }>
      recommendations: string[]
    }
    error?: string
  }> {
    try {
      console.log('Fetching sync health dashboard...')
      
      // Get overall sync statistics
      const monitoringResult = await this.getSyncMonitoring()
      if (!monitoringResult.success || !monitoringResult.data) {
        return { success: false, error: monitoringResult.error }
      }
      
      const { totalReports, syncedReports, pendingReports } = monitoringResult.data
      
      // Calculate success rate
      const syncSuccessRate = totalReports > 0 
        ? Math.round((syncedReports / totalReports) * 100) 
        : 100
      
      // Determine overall health
      let overallHealth: 'healthy' | 'warning' | 'critical' = 'healthy'
      if (syncSuccessRate < 70) {
        overallHealth = 'critical'
      } else if (syncSuccessRate < 90) {
        overallHealth = 'warning'
      }
      
      // Get device statistics to identify problem devices
      const deviceStatsResult = await this.getSyncStatsByDevice()
      const problemDevices = deviceStatsResult.success
        ? deviceStatsResult.data
            ?.filter(device => device.syncSuccessRate < 80)
            .map(device => device.deviceId) || []
        : []
      
      // Get recent sync trends (last 7 days)
      const recentSyncTrends = await this.getRecentSyncTrends()
      
      // Calculate average sync delay
      const averageSyncDelay = await this.calculateAverageSyncDelay()
      
      // Generate recommendations
      const recommendations = this.generateSyncRecommendations(
        overallHealth,
        syncSuccessRate,
        problemDevices.length,
        averageSyncDelay
      )
      
      console.log('✅ Sync health dashboard fetched successfully')
      
      return {
        success: true,
        data: {
          overallHealth,
          syncSuccessRate,
          averageSyncDelay,
          problemDevices,
          recentSyncTrends,
          recommendations
        }
      }
    } catch (err) {
      console.error('Error fetching sync health dashboard:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch sync health dashboard' 
      }
    }
  }

  /**
   * Create sync log entry
   */
  private async createSyncLogEntry(deviceId: string, reportsSynced: number): Promise<void> {
    try {
      await this.supabase
        .from('sync_logs')
        .insert([{
          device_id: deviceId,
          reports_synced: reportsSynced,
          sync_timestamp: new Date().toISOString()
        }])
    } catch (err) {
      console.warn('Failed to create sync log entry:', err)
    }
  }

  /**
   * Get recent sync trends
   */
  private async getRecentSyncTrends(): Promise<Array<{
    date: string
    syncedCount: number
    pendingCount: number
  }>> {
    try {
      const trends = []
      const today = new Date()
      
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]
        
        // Get synced count for this date
        const { count: syncedCount } = await this.supabase
          .from('reports')
          .select('*', { count: 'exact', head: true })
          .eq('sync_status', 'synced')
          .gte('updated_at', dateStr)
          .lt('updated_at', new Date(date.getTime() + 24 * 60 * 60 * 1000).toISOString())
        
        // Get pending count for this date
        const { count: pendingCount } = await this.supabase
          .from('reports')
          .select('*', { count: 'exact', head: true })
          .eq('sync_status', 'pending')
          .gte('created_at', dateStr)
          .lt('created_at', new Date(date.getTime() + 24 * 60 * 60 * 1000).toISOString())
        
        trends.push({
          date: dateStr,
          syncedCount: syncedCount || 0,
          pendingCount: pendingCount || 0
        })
      }
      
      return trends
    } catch (err) {
      console.warn('Failed to get recent sync trends:', err)
      return []
    }
  }

  /**
   * Calculate average sync delay
   */
  private async calculateAverageSyncDelay(): Promise<number> {
    try {
      const { data: reports, error } = await this.supabase
        .from('reports')
        .select('created_at, updated_at')
        .eq('sync_status', 'synced')
        .limit(100)
      
      if (error || !reports) return 0
      
      const delays = reports.map((report: any) => {
        const created = new Date(report.created_at)
        const updated = new Date(report.updated_at)
        return updated.getTime() - created.getTime()
      }).filter(delay => delay > 0)
      
      if (delays.length === 0) return 0
      
      const averageMs = delays.reduce((a, b) => a + b, 0) / delays.length
      return Math.round(averageMs / 1000) // Convert to seconds
    } catch (err) {
      console.warn('Failed to calculate average sync delay:', err)
      return 0
    }
  }

  /**
   * Generate sync recommendations
   */
  private generateSyncRecommendations(
    health: 'healthy' | 'warning' | 'critical',
    successRate: number,
    problemDeviceCount: number,
    avgDelay: number
  ): string[] {
    const recommendations = []
    
    if (health === 'critical') {
      recommendations.push('Critical: Sync success rate is below 70%. Immediate attention required.')
    } else if (health === 'warning') {
      recommendations.push('Warning: Sync success rate is below 90%. Monitor closely.')
    }
    
    if (problemDeviceCount > 0) {
      recommendations.push(`${problemDeviceCount} device(s) have sync issues. Consider investigating network connectivity.`)
    }
    
    if (avgDelay > 300) { // 5 minutes
      recommendations.push('Average sync delay is high. Consider optimizing sync frequency or data size.')
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Sync performance is healthy. Continue monitoring.')
    }
    
    return recommendations
  }
}
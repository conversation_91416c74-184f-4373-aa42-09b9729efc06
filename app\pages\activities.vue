<template>
  <div class="p-6">
    <div class="text-center">
      <UIcon name="lucide:activity" class="w-16 h-16 mx-auto mb-4 text-gray-400" />
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Activities</h1>
      <p class="text-gray-600">This page is under construction.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page metadata using layout functions
const setPageTitle = inject('setPageTitle') as (title: string) => void;
const setPageDescription = inject('setPageDescription') as (description: string) => void;
const setBreadcrumbs = inject('setBreadcrumbs') as (crumbs: Array<{ label: string, to?: string }>) => void;

// Set page information
setPageTitle('Activities');
setPageDescription('View system activities and audit logs');
setBreadcrumbs([
  { label: 'Activities' }
]);
</script>
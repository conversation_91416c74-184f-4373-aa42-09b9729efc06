/**
 * Main Supabase client composable with admin configuration
 * Provides elevated database access for desktop admin interface
 * Unlike mobile app, this composable accesses data across all devices
 */

import type { SupabaseClient } from '@supabase/supabase-js'
import type { RealtimeChannel } from '@supabase/supabase-js'
import type { AdminQueryOptions, SupabaseResponse } from '~/types/supabase'

export const useSupabase = () => {
  // Get the standard Supabase client from Nuxt module
  const supabase = useSupabaseClient()
  
  // Reactive state for connection monitoring
  const isConnected = ref(true)
  const isReconnecting = ref(false)
  const lastError = ref<string | null>(null)
  const connectionStatus = ref<'connected' | 'disconnected' | 'reconnecting'>('connected')
  
  // Track channel errors to avoid duplicate warnings
  const channelErrors: Record<string, boolean> = {}
  
  // Configuration for real-time subscriptions (disabled in dev by default due to websocket issues)
  const enableRealtime = process.env.ENABLE_REALTIME === 'true'

  /**
   * Test database connectivity
   * Used for health checks and connection monitoring
   */
  const testConnection = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('Testing Supabase connection...')
      }
      
      const { data, error } = await supabase
        .from('sites')
        .select('site_id')
        .limit(1)
      
      if (error) {
        console.error('Connection test failed:', error)
        isConnected.value = false
        lastError.value = error.message
        connectionStatus.value = 'disconnected'
        return { success: false, error: error.message }
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Supabase connection successful!')
      }
      isConnected.value = true
      lastError.value = null
      connectionStatus.value = 'connected'
      return { success: true }
    } catch (err) {
      console.error('Connection test exception:', err)
      isConnected.value = false
      lastError.value = 'Connection failed'
      connectionStatus.value = 'disconnected'
      return { success: false, error: 'Connection failed' }
    }
  }

  /**
   * Execute admin query with proper error handling
   * Admin queries have access to all data regardless of device_id
   */
  const executeAdminQuery = async <T>(
    queryFn: (client: SupabaseClient) => Promise<SupabaseResponse<T>>
  ): Promise<{ data: T | null; error: string | null; success: boolean }> => {
    try {
      const result = await queryFn(supabase)
      
      if (result.error) {
        console.error('Admin query error:', result.error)
        lastError.value = result.error.message
        return { data: null, error: result.error.message, success: false }
      }
      
      return { data: result.data, error: null, success: true }
    } catch (err) {
      console.error('Admin query exception:', err)
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      lastError.value = errorMessage
      return { data: null, error: errorMessage, success: false }
    }
  }

  /**
   * Set up real-time subscription for admin dashboard
   * Listens to changes across all tables for live updates
   */
  const setupRealtimeSubscription = (
    table: string,
    callback: (payload: any) => void
  ): RealtimeChannel | null => {
    // Skip real-time setup if disabled
    if (!enableRealtime) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`⏭️ Real-time disabled for ${table} (set ENABLE_REALTIME=true to enable)`)
      }
      return null
    }
    
    try {
      // Only log in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log(`🔄 Setting up real-time subscription for ${table}...`)
      }
      
      const channel = supabase
        .channel(`admin-${table}`)
        .on(
          'postgres_changes', 
          { event: '*', schema: 'public', table },
          (payload) => {
            if (process.env.NODE_ENV === 'development') {
              console.log(`📡 Real-time update for ${table}:`, payload)
            }
            callback(payload)
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            if (process.env.NODE_ENV === 'development') {
              console.log(`✅ Real-time active for ${table}`)
            }
          } else if (status === 'CHANNEL_ERROR' || status === 'CLOSED') {
            // Only warn once per table, and only in development
            if (process.env.NODE_ENV === 'development' && !channelErrors[table]) {
              console.warn(`⚠️ Real-time unavailable for ${table} (continuing without live updates)`)
              channelErrors[table] = true
            }
          }
        })
      
      return channel
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`⚠️ Real-time setup failed for ${table}`)
      }
      return null
    }
  }

  /**
   * Get all issues with admin-level access
   * Returns issues from all devices for comprehensive management
   */
  const getAllIssues = async (options: AdminQueryOptions = { includeAllDevices: true }) => {
    return executeAdminQuery(async (client) => {
      let query = client
        .from('reports')
        .select('*')
      
      // Apply ordering
      if (options.orderBy) {
        query = query.order(options.orderBy, { ascending: options.ascending ?? false })
      } else {
        query = query.order('created_at', { ascending: false })
      }
      
      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit)
      }
      
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
      }
      
      // Optional device filtering (admin can choose to filter by specific device)
      if (options.filterByDevice) {
        query = query.eq('device_id', options.filterByDevice)
      }
      
      return await query
    })
  }

  /**
   * Get all sites with admin access
   */
  const getAllSites = async (options: AdminQueryOptions = { includeAllDevices: true }) => {
    return executeAdminQuery(async (client) => {
      let query = client
        .from('sites')
        .select('*')
      
      if (options.orderBy) {
        query = query.order(options.orderBy, { ascending: options.ascending ?? true })
      } else {
        query = query.order('site_name', { ascending: true })
      }
      
      if (options.limit) {
        query = query.limit(options.limit)
      }
      
      return await query
    })
  }

  /**
   * Get sync monitoring data for admin dashboard
   */
  const getSyncMonitoring = async () => {
    return executeAdminQuery(async (client) => {
      return await client
        .from('sync_logs')
        .select('*')
        .order('sync_timestamp', { ascending: false })
        .limit(100)
    })
  }

  /**
   * Cleanup function for subscriptions
   */
  const cleanup = () => {
    // Remove all active subscriptions
    supabase.removeAllChannels()
    console.log('✅ All Supabase subscriptions cleaned up')
  }

  // Auto-test connection on composable initialization
  onMounted(async () => {
    await testConnection()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // Core client
    supabase,
    
    // Connection monitoring
    isConnected: readonly(isConnected),
    isReconnecting: readonly(isReconnecting),
    lastError: readonly(lastError),
    connectionStatus: readonly(connectionStatus),
    
    // Core functions
    testConnection,
    executeAdminQuery,
    setupRealtimeSubscription,
    cleanup,
    
    // Admin data access
    getAllIssues,
    getAllSites,
    getSyncMonitoring
  }
}
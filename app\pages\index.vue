<template>
  <div class="p-6">
      <!-- Dashboard Header -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p class="text-gray-600 mt-2">Welcome back! Here's what's happening with your operations.</p>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Connection Status -->
            <div class="flex items-center space-x-2">
              <div :class="isConnected ? 'w-2 h-2 bg-green-500 rounded-full' : 'w-2 h-2 bg-red-500 rounded-full'"></div>
              <span class="text-sm text-gray-600">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
            </div>
            <!-- Refresh Button -->
            <UButton 
              @click="refreshDashboard" 
              :loading="isLoading" 
              icon="lucide:refresh-cw"
              variant="outline"
              size="sm"
            >
              Refresh
            </UButton>
            <!-- Last Updated -->
            <span class="text-xs text-gray-500">
              Last updated: {{ formatTimestamp(lastRefresh) }}
            </span>
          </div>
        </div>
        
        <!-- Error Display -->
        <div v-if="error" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center">
            <UIcon name="lucide:alert-circle" class="w-5 h-5 text-red-500 mr-2" />
            <span class="text-red-700">{{ error }}</span>
          </div>
        </div>
        
        <!-- Loading State -->
        <div v-if="isLoading && !error" class="mt-4 flex items-center space-x-2">
          <UIcon name="lucide:loader-2" class="w-4 h-4 animate-spin text-blue-500" />
          <span class="text-sm text-gray-600">Loading dashboard data...</span>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Issues Card -->
        <UCard class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <template #header>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Total Issues</p>
                <p class="text-3xl font-bold text-gray-900">{{ getDashboardStats.totalIssues }}</p>
              </div>
              <div class="p-3 bg-blue-100 rounded-full">
                <UIcon name="lucide:alert-circle" class="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </template>
          <div class="text-sm text-gray-500">
            <span class="text-green-600">{{ getDashboardStats.issuesByStatus.Resolved }}</span> resolved this month
          </div>
        </UCard>

        <!-- High Priority Issues Card -->
        <UCard class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <template #header>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">High Priority</p>
                <p class="text-3xl font-bold text-red-600">{{ getDashboardStats.issuesBySeverity.High }}</p>
              </div>
              <div class="p-3 bg-red-100 rounded-full">
                <UIcon name="lucide:alert-triangle" class="w-6 h-6 text-red-600" />
              </div>
            </div>
          </template>
          <div class="text-sm text-gray-500">
            Require immediate attention
          </div>
        </UCard>

        <!-- Active Sites Card -->
        <UCard class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <template #header>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Active Sites</p>
                <p class="text-3xl font-bold text-gray-900">{{ getDashboardStats.activeSites }}</p>
              </div>
              <div class="p-3 bg-green-100 rounded-full">
                <UIcon name="lucide:building" class="w-6 h-6 text-green-600" />
              </div>
            </div>
          </template>
          <div class="text-sm text-gray-500">
            Out of {{ getDashboardStats.totalSites }} total sites
          </div>
        </UCard>

        <!-- Active Users Card -->
        <UCard class="bg-white shadow-sm hover:shadow-md transition-shadow">
          <template #header>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">Active Users</p>
                <p class="text-3xl font-bold text-gray-900">{{ getDashboardStats.activeUsers }}</p>
              </div>
              <div class="p-3 bg-purple-100 rounded-full">
                <UIcon name="lucide:users" class="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </template>
          <div class="text-sm text-gray-500">
            Currently online and active
          </div>
        </UCard>
      </div>

      <!-- Issues by Status Chart -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Issues by Status -->
        <UCard class="col-span-2">
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">Issues by Status</h3>
          </template>
          <div class="space-y-4">
            <!-- New Issues -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">New</span>
              </div>
              <div class="flex items-center">
                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div class="bg-blue-500 h-2 rounded-full" 
                       :style="{ width: (getDashboardStats.totalIssues > 0 ? (getDashboardStats.issuesByStatus.New / getDashboardStats.totalIssues * 100) : 0) + '%' }"></div>
                </div>
                <span class="text-sm font-semibold text-gray-900">{{ getDashboardStats.issuesByStatus.New }}</span>
              </div>
            </div>

            <!-- In Progress Issues -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">In Progress</span>
              </div>
              <div class="flex items-center">
                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div class="bg-yellow-500 h-2 rounded-full" 
                       :style="{ width: (getDashboardStats.totalIssues > 0 ? (getDashboardStats.issuesByStatus.InProgress / getDashboardStats.totalIssues * 100) : 0) + '%' }"></div>
                </div>
                <span class="text-sm font-semibold text-gray-900">{{ getDashboardStats.issuesByStatus.InProgress }}</span>
              </div>
            </div>

            <!-- Resolved Issues -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">Resolved</span>
              </div>
              <div class="flex items-center">
                <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div class="bg-green-500 h-2 rounded-full" 
                       :style="{ width: (getDashboardStats.totalIssues > 0 ? (getDashboardStats.issuesByStatus.Resolved / getDashboardStats.totalIssues * 100) : 0) + '%' }"></div>
                </div>
                <span class="text-sm font-semibold text-gray-900">{{ getDashboardStats.issuesByStatus.Resolved }}</span>
              </div>
            </div>
          </div>
        </UCard>

        <!-- Issues by Severity -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold text-gray-900">Issues by Severity</h3>
          </template>
          <div class="space-y-4">
            <!-- High Severity -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="lucide:alert-triangle" class="w-4 h-4 text-red-500 mr-2" />
                <span class="text-sm font-medium text-gray-700">High</span>
              </div>
              <span class="text-sm font-semibold text-red-600">{{ getDashboardStats.issuesBySeverity.High }}</span>
            </div>

            <!-- Medium Severity -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="lucide:alert-circle" class="w-4 h-4 text-yellow-500 mr-2" />
                <span class="text-sm font-medium text-gray-700">Medium</span>
              </div>
              <span class="text-sm font-semibold text-yellow-600">{{ getDashboardStats.issuesBySeverity.Medium }}</span>
            </div>

            <!-- Low Severity -->
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="lucide:info" class="w-4 h-4 text-blue-500 mr-2" />
                <span class="text-sm font-medium text-gray-700">Low</span>
              </div>
              <span class="text-sm font-semibold text-blue-600">{{ getDashboardStats.issuesBySeverity.Low }}</span>
            </div>
          </div>
        </UCard>
      </div>

      <!-- Recent Activities & Critical Issues -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Activities -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
              <UButton to="/activities" variant="ghost" size="sm">
                View All
              </UButton>
            </div>
          </template>
          <div class="space-y-4">
            <div v-for="activity in getDashboardStats.recentActivities.slice(0, 5)" 
                 :key="activity.id" 
                 class="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <UAvatar :alt="activity.userName" size="sm" class="mt-1" />
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">{{ activity.userName }}</p>
                <p class="text-sm text-gray-600">{{ activity.description }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ formatTimestamp(activity.timestamp) }}</p>
              </div>
              <UBadge 
                :color="getSeverityColor(activity.severity)" 
                variant="soft" 
                size="xs"
              >
                {{ activity.severity }}
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Critical Issues -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900">Critical Issues</h3>
              <UButton to="/issues" variant="ghost" size="sm">
                View All
              </UButton>
            </div>
          </template>
          <div class="space-y-4">
            <div v-for="issue in getDashboardStats.criticalIssues.slice(0, 5)" 
                 :key="issue.id" 
                 class="p-3 rounded-lg border border-red-200 bg-red-50">
              <div class="flex items-center justify-between mb-2">
                <h4 class="text-sm font-medium text-gray-900">{{ issue.title }}</h4>
                <UBadge color="error" variant="solid" size="xs">
                  {{ issue.severity }}
                </UBadge>
              </div>
              <p class="text-sm text-gray-600 mb-2">{{ issue.description }}</p>
              <div class="flex items-center justify-between text-xs text-gray-500">
                <span>{{ issue.siteName }}</span>
                <span>{{ formatTimestamp(issue.createdAt) }}</span>
              </div>
            </div>
            
            <div v-if="getDashboardStats.criticalIssues.length === 0" 
                 class="text-center py-8 text-gray-500">
              <UIcon name="lucide:check-circle" class="w-12 h-12 mx-auto mb-2 text-green-500" />
              <p>No critical issues at this time</p>
            </div>
          </div>
        </UCard>
      </div>
    </div>
</template>

<script setup lang="ts">
import { IssueSeverity, IssueStatus, type DashboardStats, type Activity } from '~/types';
import { IssueService } from '~/services/IssueService';
import { SiteService } from '~/services/SiteService';
import { SyncService } from '~/services/SyncService';

// Page configuration

// Get composables for data management
const { supabase, isConnected, testConnection } = useSupabase();
const { isAuthenticated, hasPermission } = useAuth();
const { issues, isLoading: issuesLoading, error: issuesError } = useIssues();
const { sites, isLoading: sitesLoading, error: sitesError } = useSites();

// Initialize service instances
const issueService = new IssueService(supabase);
const siteService = new SiteService(supabase);
const syncService = new SyncService(supabase);

// Reactive dashboard data
const dashboardStats = ref<DashboardStats | null>(null);
const isLoading = ref(true);
const error = ref<string | null>(null);
const lastRefresh = ref<Date>(new Date());

/**
 * Load real dashboard data from services
 * Aggregates data from multiple sources for comprehensive dashboard
 */
const loadDashboardData = async (): Promise<void> => {
  try {
    isLoading.value = true;
    error.value = null;
    console.log('Loading dashboard data from real backend...');

    // Load data in parallel for better performance
    const [issueStatsResult, siteStatsResult, syncMonitoringResult] = await Promise.all([
      issueService.getIssueStatistics(),
      siteService.getSiteStatistics(),
      syncService.getSyncMonitoring()
    ]);

    // Check for errors
    if (!issueStatsResult.success) {
      throw new Error(`Issue stats error: ${issueStatsResult.error}`);
    }
    if (!siteStatsResult.success) {
      throw new Error(`Site stats error: ${siteStatsResult.error}`);
    }
    if (!syncMonitoringResult.success) {
      console.warn('Sync monitoring error:', syncMonitoringResult.error);
    }

    // Build dashboard statistics
    const issueStats = issueStatsResult.data!;
    const siteStats = siteStatsResult.data!;
    const syncData = syncMonitoringResult.data;

    // Generate recent activities from issues and sites
    const recentActivities: Activity[] = [];
    
    // Add critical issues as activities
    issueStats.criticalIssues.forEach((issue, index) => {
      recentActivities.push({
        id: `activity-issue-${issue.id}`,
        userId: 'system',
        userName: 'System',
        userRole: 'Admin',
        action: 'Critical Issue',
        description: `High priority issue: ${issue.title}`,
        entityType: 'Issue',
        entityId: issue.id,
        entityName: issue.title,
        timestamp: new Date(issue.timestamp),
        severity: issue.severity,
        isSystemGenerated: true
      });
    });

    // Create dashboard stats object
    dashboardStats.value = {
      totalIssues: issueStats.total,
      issuesBySeverity: issueStats.bySeverity,
      issuesByStatus: issueStats.byStatus,
      totalSites: siteStats.total,
      activeSites: siteStats.active,
      totalUsers: syncData?.activeDevices.length || 0, // Use active devices as user count
      activeUsers: syncData?.activeDevices.length || 0,
      recentActivities: recentActivities.slice(0, 10),
      criticalIssues: issueStats.criticalIssues,
      upcomingInspections: [] // Would be populated from inspection scheduling
    };

    lastRefresh.value = new Date();
    console.log('✅ Dashboard data loaded successfully');
  } catch (err) {
    console.error('Error loading dashboard data:', err);
    error.value = err instanceof Error ? err.message : 'Failed to load dashboard data';
  } finally {
    isLoading.value = false;
  }
};

/**
 * Refresh dashboard data
 * Manually trigger data reload
 */
const refreshDashboard = async (): Promise<void> => {
  await loadDashboardData();
};

// Initialize dashboard data on component mount
onMounted(async () => {
  console.log('Initializing dashboard...');
  
  // Test connection first
  const connectionResult = await testConnection();
  if (connectionResult.success) {
    console.log('✅ Supabase connection successful!');
    
    // Load dashboard data
    await loadDashboardData();
  } else {
    console.error('❌ Supabase connection failed:', connectionResult.error);
    error.value = 'Database connection failed. Please check your connection and try again.';
    isLoading.value = false;
  }
});

/**
 * Format timestamp for display
 */
const formatTimestamp = (timestamp: Date | string | undefined): string => {
  if (!timestamp) {
    return 'N/A';
  }
  
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  
  // Check if date is valid
  if (isNaN(date.getTime())) {
    return 'Invalid date';
  }
  
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 60) {
    return `${minutes}m ago`;
  } else if (hours < 24) {
    return `${hours}h ago`;
  } else {
    return `${days}d ago`;
  }
};

/**
 * Get color based on severity level
 */
const getSeverityColor = (severity: IssueSeverity): 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | 'neutral' => {
  switch (severity) {
    case IssueSeverity.High:
      return 'error';
    case IssueSeverity.Medium:
      return 'warning';
    case IssueSeverity.Low:
      return 'info';
    default:
      return 'neutral';
  }
};

/**
 * Get dashboard statistics with loading fallback
 */
const getDashboardStats = computed(() => {
  if (isLoading.value || !dashboardStats.value) {
    return {
      totalIssues: 0,
      issuesBySeverity: {
        High: 0,
        Medium: 0,
        Low: 0
      },
      issuesByStatus: {
        New: 0,
        InProgress: 0,
        Resolved: 0,
        Assigned: 0
      },
      totalSites: 0,
      activeSites: 0,
      totalUsers: 0,
      activeUsers: 0,
      recentActivities: [],
      criticalIssues: [],
      upcomingInspections: []
    };
  }
  return dashboardStats.value;
});
</script>

<style scoped>
/* Custom styles for dashboard cards */
.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dashboard-card-content {
  backdrop-filter: blur(10px);
}
</style>
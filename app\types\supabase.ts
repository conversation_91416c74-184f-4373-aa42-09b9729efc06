// Supabase-specific TypeScript types for database schema alignment
// Ensures compatibility with mobile app database structure

/**
 * Database table interfaces matching Supabase schema
 */

/**
 * Reports table interface (matches Supabase 'reports' table)
 */
export interface ReportsTable {
  id: string; // UUID PRIMARY KEY
  device_id: string; // UUID NOT NULL
  title: string; // TEXT NOT NULL
  description: string; // TEXT
  location: string; // TEXT (maps to siteId)
  severity: 'Low' | 'Medium' | 'High'; // TEXT
  status: 'New' | 'Assigned' | 'In Progress' | 'Resolved'; // TEXT
  created_at: string; // TIMESTAMP WITH TIME ZONE
  updated_at: string; // TIMESTAMP WITH TIME ZONE
  sync_status: 'pending' | 'synced'; // TEXT DEFAULT 'pending'
  category?: string; // TEXT (for category enum)
}

/**
 * Sites table interface (matches Supabase 'sites' table)
 */
export interface SitesTable {
  site_id: string; // TEXT PRIMARY KEY
  site_name: string; // TEXT NOT NULL
  site_address: {
    street: string;
    latitude: number;
    longitude: number;
    city: string;
    state: string;
    country: string;
  }; // JSONB
  status: 'Active' | 'Support' | 'Not Active' | 'Dismantled'; // TEXT
  created_at: string; // TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  updated_at: string; // TIMESTAMP WITH TIME ZONE DEFAULT NOW()
}

/**
 * Sync logs table interface (matches Supabase 'sync_logs' table)
 */
export interface SyncLogsTable {
  id: string; // UUID PRIMARY KEY DEFAULT gen_random_uuid()
  device_id: string; // UUID NOT NULL
  reports_synced: number; // INTEGER
  sync_timestamp: string; // TIMESTAMP WITH TIME ZONE DEFAULT NOW()
}

/**
 * Storage bucket structure for photos
 */
export interface PhotoStorage {
  bucket_name: 'report-photos';
  path_pattern: string; // '{reportId}/{photoId}.jpg'
  public_access: 'read-only-authenticated';
}

/**
 * Supabase client configuration interface
 */
export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
}

/**
 * Database query response types
 */
export interface SupabaseResponse<T> {
  data: T | null;
  error: {
    message: string;
    details: string;
    hint: string;
    code: string;
  } | null;
}

/**
 * Real-time subscription payload types
 */
export interface RealtimePayload<T = any> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any[];
}

/**
 * Admin query patterns for cross-device data access
 */
export interface AdminQueryOptions {
  includeAllDevices: boolean; // Admin sees all device_ids
  filterByDevice?: string; // Optional device filtering
  orderBy?: 'created_at' | 'updated_at' | 'severity' | 'status';
  ascending?: boolean;
  limit?: number;
  offset?: number;
}

/**
 * Sync monitoring interface for admin dashboard
 */
export interface SyncMonitoring {
  totalReports: number;
  syncedReports: number;
  pendingReports: number;
  lastSyncTime: string;
  activeDevices: string[];
  syncErrors: {
    device_id: string;
    error_message: string;
    timestamp: string;
  }[];
}

/**
 * Database schema validation interface
 */
export interface SchemaValidation {
  reportsTableExists: boolean;
  sitesTableExists: boolean;
  syncLogsTableExists: boolean;
  photoBucketExists: boolean;
  requiredColumnsPresent: boolean;
  indexesConfigured: boolean;
}
/**
 * File/photo management composable for report attachments
 * Handles Supabase Storage operations with optimized image handling
 * Provides upload, download, and management for issue report photos
 */

import type { Photo } from '~/types'

export const useStorage = () => {
  // Get Supabase client
  const { supabase } = useSupabase()
  const { hasPermission } = useAuth()
  
  // Reactive state for storage operations
  const isUploading = ref(false)
  const isDownloading = ref(false)
  const uploadProgress = ref(0)
  const error = ref<string | null>(null)
  
  // Storage configuration
  const BUCKET_NAME = 'report-photos'
  const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  const SUPPORTED_FORMATS = ['image/jpeg', 'image/png', 'image/webp']

  /**
   * Upload photo to Supabase Storage
   * Optimizes image and creates proper file path structure
   */
  const uploadPhoto = async (
    file: File, 
    issueId: string, 
    title?: string
  ): Promise<{ success: boolean; data?: Photo; error?: string }> => {
    try {
      if (!hasPermission('issues.update')) {
        return { success: false, error: 'Permission denied: Cannot upload photos' }
      }
      
      isUploading.value = true
      uploadProgress.value = 0
      error.value = null
      
      console.log('Uploading photo for issue:', issueId)
      
      // Validate file
      const validation = validateFile(file)
      if (!validation.isValid) {
        return { success: false, error: validation.error }
      }
      
      // Generate unique photo ID and file path
      const photoId = `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const fileExtension = getFileExtension(file.name)
      const filePath = `${issueId}/${photoId}.${fileExtension}`
      
      // Optimize image before upload
      const optimizedFile = await optimizeImage(file)
      
      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from(BUCKET_NAME)
        .upload(filePath, optimizedFile, {
          cacheControl: '3600',
          upsert: false
        })
      
      if (uploadError) {
        console.error('Upload error:', uploadError)
        error.value = uploadError.message
        return { success: false, error: uploadError.message }
      }
      
      uploadProgress.value = 100
      
      // Get public URL for the uploaded file
      const { data: { publicUrl } } = supabase.storage
        .from(BUCKET_NAME)
        .getPublicUrl(filePath)
      
      const photo: Photo = {
        id: photoId,
        uri: publicUrl,
        timestamp: new Date().toISOString(),
        title: title || file.name
      }
      
      console.log('✅ Photo uploaded successfully:', photoId)
      return { success: true, data: photo }
    } catch (err) {
      console.error('Exception while uploading photo:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload photo'
      error.value = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isUploading.value = false
      uploadProgress.value = 0
    }
  }

  /**
   * Upload multiple photos at once
   * Handles batch uploads with progress tracking
   */
  const uploadMultiplePhotos = async (
    files: File[], 
    issueId: string
  ): Promise<{ success: boolean; data?: Photo[]; error?: string; partial?: boolean }> => {
    try {
      if (!hasPermission('issues.update')) {
        return { success: false, error: 'Permission denied: Cannot upload photos' }
      }
      
      isUploading.value = true
      error.value = null
      
      console.log(`Uploading ${files.length} photos for issue:`, issueId)
      
      const uploadedPhotos: Photo[] = []
      const failedUploads: string[] = []
      let completedCount = 0
      
      for (const file of files) {
        uploadProgress.value = Math.round((completedCount / files.length) * 100)
        
        const result = await uploadPhoto(file, issueId)
        
        if (result.success && result.data) {
          uploadedPhotos.push(result.data)
        } else {
          failedUploads.push(`${file.name}: ${result.error}`)
        }
        
        completedCount++
      }
      
      uploadProgress.value = 100
      
      if (failedUploads.length === 0) {
        console.log(`✅ All ${uploadedPhotos.length} photos uploaded successfully`)
        return { success: true, data: uploadedPhotos }
      } else if (uploadedPhotos.length > 0) {
        console.log(`⚠️ Partial upload: ${uploadedPhotos.length} succeeded, ${failedUploads.length} failed`)
        return { 
          success: true, 
          data: uploadedPhotos, 
          partial: true,
          error: `Some uploads failed: ${failedUploads.join(', ')}`
        }
      } else {
        console.error('All uploads failed:', failedUploads)
        return { success: false, error: `All uploads failed: ${failedUploads.join(', ')}` }
      }
    } catch (err) {
      console.error('Exception while uploading multiple photos:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload photos'
      error.value = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isUploading.value = false
      uploadProgress.value = 0
    }
  }

  /**
   * Delete photo from Supabase Storage
   * Removes file and cleans up storage
   */
  const deletePhoto = async (photoId: string, issueId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!hasPermission('issues.update')) {
        return { success: false, error: 'Permission denied: Cannot delete photos' }
      }
      
      console.log('Deleting photo:', photoId)
      
      // Find the file path (we need to know the extension)
      // In a real implementation, you might store the full path or query it
      const possibleExtensions = ['jpg', 'jpeg', 'png', 'webp']
      let deleteSuccess = false
      
      for (const ext of possibleExtensions) {
        const filePath = `${issueId}/${photoId}.${ext}`
        
        const { error: deleteError } = await supabase.storage
          .from(BUCKET_NAME)
          .remove([filePath])
        
        if (!deleteError) {
          deleteSuccess = true
          break
        }
      }
      
      if (deleteSuccess) {
        console.log('✅ Photo deleted successfully:', photoId)
        return { success: true }
      } else {
        console.error('Failed to delete photo:', photoId)
        return { success: false, error: 'Failed to delete photo' }
      }
    } catch (err) {
      console.error('Exception while deleting photo:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete photo'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Get download URL for a photo
   * Creates signed URL for temporary access
   */
  const getPhotoDownloadUrl = async (filePath: string, expiresIn = 3600): Promise<{ success: boolean; url?: string; error?: string }> => {
    try {
      isDownloading.value = true
      
      const { data, error: signError } = await supabase.storage
        .from(BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn)
      
      if (signError) {
        console.error('Error creating signed URL:', signError)
        return { success: false, error: signError.message }
      }
      
      return { success: true, url: data.signedUrl }
    } catch (err) {
      console.error('Exception while getting download URL:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to get download URL'
      return { success: false, error: errorMessage }
    } finally {
      isDownloading.value = false
    }
  }

  /**
   * List all photos for an issue
   * Returns file list from storage bucket
   */
  const listIssuePhotos = async (issueId: string): Promise<{ success: boolean; data?: string[]; error?: string }> => {
    try {
      const { data, error: listError } = await supabase.storage
        .from(BUCKET_NAME)
        .list(issueId)
      
      if (listError) {
        console.error('Error listing photos:', listError)
        return { success: false, error: listError.message }
      }
      
      const photoFiles = data?.map(file => `${issueId}/${file.name}`) || []
      return { success: true, data: photoFiles }
    } catch (err) {
      console.error('Exception while listing photos:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to list photos'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Validate uploaded file
   * Checks file size, format, and other constraints
   */
  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return { 
        isValid: false, 
        error: `File size too large. Maximum size is ${Math.round(MAX_FILE_SIZE / 1024 / 1024)}MB` 
      }
    }
    
    // Check file format
    if (!SUPPORTED_FORMATS.includes(file.type)) {
      return { 
        isValid: false, 
        error: `Unsupported file format. Supported formats: ${SUPPORTED_FORMATS.join(', ')}` 
      }
    }
    
    return { isValid: true }
  }

  /**
   * Optimize image before upload
   * Compresses and resizes image for efficient storage
   */
  const optimizeImage = async (file: File): Promise<File> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // Calculate optimal dimensions (max 1920x1080)
        const maxWidth = 1920
        const maxHeight = 1080
        let { width, height } = img
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width = Math.round(width * ratio)
          height = Math.round(height * ratio)
        }
        
        canvas.width = width
        canvas.height = height
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const optimizedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              })
              resolve(optimizedFile)
            } else {
              resolve(file) // Fallback to original file
            }
          },
          'image/jpeg',
          0.85 // 85% quality
        )
      }
      
      img.onerror = () => {
        resolve(file) // Fallback to original file
      }
      
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Get file extension from filename
   */
  const getFileExtension = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase()
    return extension || 'jpg'
  }

  /**
   * Format file size for display
   */
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return {
    // State
    isUploading: readonly(isUploading),
    isDownloading: readonly(isDownloading),
    uploadProgress: readonly(uploadProgress),
    error: readonly(error),
    
    // Upload operations
    uploadPhoto,
    uploadMultiplePhotos,
    
    // Storage operations
    deletePhoto,
    getPhotoDownloadUrl,
    listIssuePhotos,
    
    // Utilities
    validateFile,
    formatFileSize,
    
    // Configuration
    BUCKET_NAME: readonly(ref(BUCKET_NAME)),
    MAX_FILE_SIZE: readonly(ref(MAX_FILE_SIZE)),
    SUPPORTED_FORMATS: readonly(ref(SUPPORTED_FORMATS))
  }
}
// Mock data for the OMS Desktop Admin Interface
// Updated to match mobile app schema exactly

import type { 
  IssueReport,
  Site, 
  User, 
  Activity, 
  DashboardStats, 
  NavMenuItem,
  Photo,
  SiteAddress
} from '~/types';

// Import enums as values (not types) since they're used at runtime
import { 
  IssueCategory,
  IssueSeverity,
  IssueStatus,
  SiteStatus
} from '~/types';

/**
 * Mock users data
 */
export const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    firstName: '<PERSON>',
    lastName: 'Admin',
    role: 'Admin',
    phone: '******-0101',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-07-10'),
    lastLogin: new Date('2024-07-10T08:00:00Z'),
    department: 'IT',
    siteAssignments: ['1', '2', '3'],
    skills: ['System Administration', 'Database Management'],
    certifications: ['AWS Certified', 'Azure Fundamentals'],
    emergencyContact: {
      name: '<PERSON>',
      phone: '******-0102',
      relationship: 'Spouse'
    }
  },
  {
    id: '2',
    username: 'manager1',
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Manager',
    role: 'Manager',
    phone: '******-0201',
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-07-09'),
    lastLogin: new Date('2024-07-09T16:30:00Z'),
    department: 'Operations',
    siteAssignments: ['1', '2'],
    skills: ['Project Management', 'Team Leadership'],
    certifications: ['PMP', 'Six Sigma Green Belt']
  },
  {
    id: '3',
    username: 'tech1',
    email: '<EMAIL>',
    firstName: 'Mike',
    lastName: 'Technician',
    role: 'Technician',
    phone: '******-0301',
    isActive: true,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-07-08'),
    lastLogin: new Date('2024-07-08T14:20:00Z'),
    department: 'Maintenance',
    siteAssignments: ['1'],
    skills: ['Electrical', 'HVAC', 'Plumbing'],
    certifications: ['Electrical License', 'HVAC Certification']
  }
];

/**
 * Mock sites data matching mobile app schema
 */
export const mockSites: Site[] = [
  {
    siteId: '1',
    siteName: 'Downtown Office Building',
    siteAddress: {
      street: '123 Main Street',
      latitude: 40.7128,
      longitude: -74.0060,
      city: 'New York',
      state: 'NY',
      country: 'USA'
    },
    status: SiteStatus.Active
  },
  {
    siteId: '2',
    siteName: 'Warehouse District A',
    siteAddress: {
      street: '456 Industrial Ave',
      latitude: 40.7357,
      longitude: -74.1724,
      city: 'Newark',
      state: 'NJ',
      country: 'USA'
    },
    status: SiteStatus.Active
  },
  {
    siteId: '3',
    siteName: 'Retail Store Central',
    siteAddress: {
      street: '789 Shopping Blvd',
      latitude: 40.7282,
      longitude: -74.0776,
      city: 'Jersey City',
      state: 'NJ',
      country: 'USA'
    },
    status: SiteStatus.Support
  },
  {
    siteId: '4',
    siteName: 'Manufacturing Plant North',
    siteAddress: {
      street: '101 Factory Road',
      latitude: 40.8176,
      longitude: -74.0431,
      city: 'Bronx',
      state: 'NY',
      country: 'USA'
    },
    status: SiteStatus.NotActive
  }
];

/**
 * Mock photo data
 */
export const mockPhotos: Photo[] = [
  {
    id: 'photo-1',
    uri: '/images/hvac-issue-1.jpg',
    timestamp: '2024-07-08T09:15:00Z',
    title: 'HVAC Unit Overview'
  },
  {
    id: 'photo-2',
    uri: '/images/hvac-issue-2.jpg',
    timestamp: '2024-07-08T09:16:00Z',
    title: 'Compressor Close-up'
  },
  {
    id: 'photo-3',
    uri: '/images/electrical-outlet.jpg',
    timestamp: '2024-07-09T11:20:00Z',
    title: 'Non-functioning Outlet'
  },
  {
    id: 'photo-4',
    uri: '/images/water-leak-before.jpg',
    timestamp: '2024-07-05T14:25:00Z',
    title: 'Water Leak Before Repair'
  },
  {
    id: 'photo-5',
    uri: '/images/water-leak-after.jpg',
    timestamp: '2024-07-07T10:50:00Z',
    title: 'Water Leak After Repair'
  }
];

/**
 * Mock issue reports data matching mobile app schema
 */
export const mockIssueReports: IssueReport[] = [
  {
    id: '1',
    title: 'HVAC System Malfunction',
    category: IssueCategory.Corrective,
    description: 'Air conditioning system on 5th floor not working properly. Temperature fluctuating between 65-80°F. Compressor making unusual noise.',
    siteId: '1',
    timestamp: '2024-07-08T09:00:00Z',
    severity: IssueSeverity.High,
    status: IssueStatus.InProgress,
    photos: [
      {
        id: 'photo-1',
        uri: '/images/hvac-issue-1.jpg',
        timestamp: '2024-07-08T09:15:00Z',
        title: 'HVAC Unit Overview'
      },
      {
        id: 'photo-2',
        uri: '/images/hvac-issue-2.jpg',
        timestamp: '2024-07-08T09:16:00Z',
        title: 'Compressor Close-up'
      }
    ],
    updates: [
      {
        timestamp: '2024-07-09T14:30:00Z',
        description: 'Issue assigned to technician. Replacement parts ordered.',
        photos: [],
        previousStatus: IssueStatus.New,
        newStatus: IssueStatus.InProgress,
        technician: 'Mike Technician'
      }
    ],
    sync_status: 'synced',
    device_id: 'device-admin-001'
  },
  {
    id: '2',
    title: 'Electrical Outlet Not Working',
    category: IssueCategory.Corrective,
    description: 'Power outlet in conference room B is not providing electricity. Multiple devices affected. Outlet tested with multimeter - no power detected.',
    siteId: '1',
    timestamp: '2024-07-09T11:15:00Z',
    severity: IssueSeverity.Medium,
    status: IssueStatus.New,
    photos: [
      {
        id: 'photo-3',
        uri: '/images/electrical-outlet.jpg',
        timestamp: '2024-07-09T11:20:00Z',
        title: 'Non-functioning Outlet'
      }
    ],
    sync_status: 'synced',
    device_id: 'device-admin-001'
  },
  {
    id: '3',
    title: 'Water Leak in Bathroom',
    category: IssueCategory.Corrective,
    description: 'Small water leak detected under sink in women\'s restroom. Floor getting wet.',
    siteId: '2',
    timestamp: '2024-07-05T14:20:00Z',
    severity: IssueSeverity.Medium,
    status: IssueStatus.Resolved,
    photos: [
      {
        id: 'photo-4',
        uri: '/images/water-leak-before.jpg',
        timestamp: '2024-07-05T14:25:00Z',
        title: 'Water Leak Before Repair'
      },
      {
        id: 'photo-5',
        uri: '/images/water-leak-after.jpg',
        timestamp: '2024-07-07T10:50:00Z',
        title: 'Water Leak After Repair'
      }
    ],
    updates: [
      {
        timestamp: '2024-07-06T08:00:00Z',
        description: 'Issue assigned to technician.',
        photos: [],
        previousStatus: IssueStatus.New,
        newStatus: IssueStatus.Assigned,
        technician: 'Mike Technician'
      },
      {
        timestamp: '2024-07-07T10:45:00Z',
        description: 'Pipe gasket replaced. No further leaks detected.',
        photos: [
          {
            id: 'photo-5',
            uri: '/images/water-leak-after.jpg',
            timestamp: '2024-07-07T10:50:00Z',
            title: 'Water Leak After Repair'
          }
        ],
        previousStatus: IssueStatus.Assigned,
        newStatus: IssueStatus.Resolved,
        technician: 'Mike Technician'
      }
    ],
    sync_status: 'synced',
    device_id: 'device-mobile-002'
  },
  {
    id: '4',
    title: 'Broken Window Lock',
    category: IssueCategory.Corrective,
    description: 'Security window lock mechanism broken on 2nd floor office window. Window lock handle appears to be stripped.',
    siteId: '3',
    timestamp: '2024-07-09T16:30:00Z',
    severity: IssueSeverity.Low,
    status: IssueStatus.New,
    photos: [],
    sync_status: 'synced',
    device_id: 'device-admin-001'
  },
  {
    id: '5',
    title: 'Lighting Fixture Flickering',
    category: IssueCategory.Corrective,
    description: 'Fluorescent light fixture in main hallway flickering intermittently. Ballast likely needs replacement.',
    siteId: '1',
    timestamp: '2024-07-06T08:45:00Z',
    severity: IssueSeverity.Low,
    status: IssueStatus.InProgress,
    photos: [],
    updates: [
      {
        timestamp: '2024-07-08T13:20:00Z',
        description: 'Issue assigned to technician. Ordered new ballast unit.',
        photos: [],
        previousStatus: IssueStatus.New,
        newStatus: IssueStatus.InProgress,
        technician: 'Mike Technician'
      }
    ],
    sync_status: 'synced',
    device_id: 'device-mobile-003'
  },
  {
    id: '6',
    title: 'Preventive Maintenance - Fire Extinguisher Check',
    category: IssueCategory.Preventive,
    description: 'Monthly fire extinguisher inspection and testing required.',
    siteId: '2',
    timestamp: '2024-07-10T09:00:00Z',
    severity: IssueSeverity.Medium,
    status: IssueStatus.New,
    photos: [],
    sync_status: 'pending',
    device_id: 'device-mobile-001'
  },
  {
    id: '7',
    title: 'Vandalism - Graffiti on Exterior Wall',
    category: IssueCategory.Vandalism,
    description: 'Graffiti found on the north exterior wall of the building.',
    siteId: '3',
    timestamp: '2024-07-09T18:00:00Z',
    severity: IssueSeverity.Low,
    status: IssueStatus.New,
    photos: [],
    sync_status: 'synced',
    device_id: 'device-mobile-004'
  },
  {
    id: '8',
    title: 'Audit Finding - Emergency Exit Sign',
    category: IssueCategory.Audit,
    description: 'Emergency exit sign not properly illuminated during safety audit.',
    siteId: '1',
    timestamp: '2024-07-08T16:00:00Z',
    severity: IssueSeverity.High,
    status: IssueStatus.Assigned,
    photos: [],
    sync_status: 'synced',
    device_id: 'device-admin-001'
  }
];

/**
 * Mock activities data
 */
export const mockActivities: Activity[] = [
  {
    id: '1',
    userId: '3',
    userName: 'Mike Technician',
    userRole: 'Technician',
    action: 'Issue Updated',
    description: 'Updated issue status to In Progress',
    entityType: 'Issue',
    entityId: '1',
    entityName: 'HVAC System Malfunction',
    timestamp: new Date('2024-07-09T14:30:00Z'),
    changes: [
      { field: 'status', oldValue: 'New', newValue: 'In Progress' },
      { field: 'assignedTo', oldValue: null, newValue: 'Mike Technician' }
    ],
    severity: 'Medium',
    isSystemGenerated: false
  },
  {
    id: '2',
    userId: '2',
    userName: 'Sarah Manager',
    userRole: 'Manager',
    action: 'Issue Created',
    description: 'Created new issue for electrical outlet',
    entityType: 'Issue',
    entityId: '2',
    entityName: 'Electrical Outlet Not Working',
    timestamp: new Date('2024-07-09T11:15:00Z'),
    severity: 'Low',
    isSystemGenerated: false
  },
  {
    id: '3',
    userId: '3',
    userName: 'Mike Technician',
    userRole: 'Technician',
    action: 'Issue Resolved',
    description: 'Resolved water leak issue',
    entityType: 'Issue',
    entityId: '3',
    entityName: 'Water Leak in Bathroom',
    timestamp: new Date('2024-07-07T10:45:00Z'),
    changes: [
      { field: 'status', oldValue: 'In Progress', newValue: 'Resolved' }
    ],
    severity: 'Medium',
    isSystemGenerated: false
  },
  {
    id: '4',
    userId: '1',
    userName: 'John Admin',
    userRole: 'Admin',
    action: 'User Login',
    description: 'User logged into system',
    entityType: 'User',
    entityId: '1',
    entityName: 'John Admin',
    timestamp: new Date('2024-07-10T08:00:00Z'),
    severity: 'Low',
    isSystemGenerated: true
  },
  {
    id: '5',
    userId: '2',
    userName: 'Sarah Manager',
    userRole: 'Manager',
    action: 'Site Inspection',
    description: 'Completed routine site inspection',
    entityType: 'Site',
    entityId: '1',
    entityName: 'Downtown Office Building',
    timestamp: new Date('2024-06-15T15:00:00Z'),
    severity: 'Low',
    isSystemGenerated: false
  }
];

/**
 * Mock navigation menu items
 */
export const mockNavMenuItems: NavMenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'lucide:layout-dashboard',
    route: '/',
    isActive: true
  },
  {
    id: 'issues',
    label: 'Issues',
    icon: 'lucide:alert-circle',
    route: '/issues',
    badge: 11
  },
  {
    id: 'sites',
    label: 'Sites',
    icon: 'lucide:building',
    route: '/sites'
  },
  {
    id: 'users',
    label: 'Users',
    icon: 'lucide:users',
    route: '/users'
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: 'lucide:file-text',
    route: '/reports'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: 'lucide:settings',
    route: '/settings'
  }
];

/**
 * Generate dashboard statistics from mock data
 */
export const generateDashboardStats = (): DashboardStats => {
  // Calculate issue statistics
  const totalIssues = mockIssueReports.length;
  const issuesBySeverity = mockIssueReports.reduce((acc, issue) => {
    acc[issue.severity]++;
    return acc;
  }, { [IssueSeverity.High]: 0, [IssueSeverity.Medium]: 0, [IssueSeverity.Low]: 0 });
  
  const issuesByStatus = mockIssueReports.reduce((acc, issue) => {
    acc[issue.status]++;
    return acc;
  }, { [IssueStatus.New]: 0, [IssueStatus.InProgress]: 0, [IssueStatus.Resolved]: 0, [IssueStatus.Assigned]: 0 });

  // Calculate site statistics
  const totalSites = mockSites.length;
  const activeSites = mockSites.filter(site => site.isActive).length;

  // Calculate user statistics
  const totalUsers = mockUsers.length;
  const activeUsers = mockUsers.filter(user => user.isActive).length;

  // Get critical issues (High severity)
  const criticalIssues = mockIssueReports.filter(issue => issue.severity === IssueSeverity.High).slice(0, 5);

  // Get recent activities (last 10)
  const recentActivities = mockActivities.slice(0, 10);

  // Get upcoming inspections (filtered to active sites)
  const upcomingInspections = mockSites
    .filter(site => site.status === SiteStatus.Active)
    .slice(0, 5);

  return {
    totalIssues,
    issuesBySeverity,
    issuesByStatus,
    totalSites,
    activeSites,
    totalUsers,
    activeUsers,
    recentActivities,
    criticalIssues,
    upcomingInspections
  };
};

/**
 * Mock dashboard statistics
 */
export const mockDashboardStats: DashboardStats = generateDashboardStats();
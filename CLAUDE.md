# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
- This needs to be a simple Desktop Admin for the react native mobile app. It needs to have the same functionality as the mobile app, but with a different UI that is more suitable for a desktop application.

## Main Reference
- **React Native Mobile App**: '/mnt/c/Users/<USER>/codejr/rn_xolas_oms' - This is the main reference for functionality and features to replicate in the desktop admin interface.
- **Desktop Admin App**: 'Desktop Admin Interface Plan' from 'CLAUDE.local.md' - This is the main reference for functionality and features to implement in the desktop admin interface.

## Project Preferences
- always ensure that the code is written in TypeScript, and ensure type safety before commiting.
- always include comments in the code for each block describing the functionality.
- always include logs in the code for better debugging and troubleshooting.
- always use mock data, unless a valid connection is available.
- always refer to 'https://nuxt.com/docs/' for the Nuxt documentation.
- always refer to 'https://ui.nuxt.com/components/' for the UI components, and 'https://ui.nuxt.com/components/' for the documentation.
- always refer to 'https://icon-sets.iconify.design/lucide/' for the icons.
- always ask me to manually start the dev server, and provide the output. 
- always request to manually install any dependecy packages.

## Development Commands

### Setup
```bash
bun install
```

### Development
```bash
bun run dev          # Start development server on localhost:3000
```

### Build & Production
```bash
bun run build        # Build for production
bun run preview      # Preview production build locally
bun run generate     # Generate static site
```

### Code Quality
```bash
# ESLint commands not yet configured in package.json
```

### Post-install
```bash
bun run postinstall  # Prepare Nuxt (runs automatically after install)
```

## Project Structure
- `app/` - Main application directory
  - `app.vue` - Root Vue component with UApp wrapper
  - `assets/css/` - CSS files including Tailwind imports
    - `main.css` - Main CSS file with Tailwind imports
- `nuxt.config.ts` - Main Nuxt configuration
- `server/` - Server-side code (with separate tsconfig.json)
- `public/` - Static assets
  - `favicon.ico` - Site favicon
  - `robots.txt` - Robots.txt file
- `CLAUDE.md` - Main project documentation
- `CLAUDE.local.md` - Local configuration
- `TODO_Backend Integration.md` - Backend integration plan
- `TODO_Issues Management.md` - Issues management plan

## Technology Stack
- **Framework**: Nuxt 3 (v3.17.6)
- **UI Library**: Nuxt UI (v3.2.0) with Tailwind CSS
- **Content Management**: Nuxt Content (v3.6.3)
- **Fonts**: Nuxt Fonts (v0.11.4)
- **Icons**: Nuxt Icon (v1.15.0) with Lucide and Simple Icons
- **Image Optimization**: Nuxt Image (v1.10.0)
- **TypeScript**: Fully configured with strict typing (v5.6.3)
- **Linting**: ESLint (v9.0.0) with Nuxt ESLint module (v1.5.2)
- **Package Manager**: Bun (lockfile: bun.lockb)

## Configuration Notes
- ESLint configuration in `eslint.config.mjs`
- TypeScript configuration in `tsconfig.json` (root) and `server/tsconfig.json`
- Nuxt configuration in `nuxt.config.ts`
- CSS configuration in `app/assets/css/main.css`

## Key Features
- Nuxt 3 framework with modern Vue 3 composition API
- Nuxt UI components for consistent design system
- Content management with Nuxt Content
- Font optimization with Nuxt Fonts
- Icon system with Nuxt Icon (Lucide and Simple Icons)
- Image optimization with Nuxt Image
- TypeScript support throughout
- ESLint integration for code quality

## Current Status
- Basic project structure initialized
- Dependencies installed and configured
- Ready for pages and components development
- No file-based routing pages created yet
- App configuration file not yet created
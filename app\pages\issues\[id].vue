<template>
  <div class="flex flex-col h-full">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="text-center">
        <UIcon name="lucide:loader-2" class="w-8 h-8 animate-spin mx-auto mb-2 text-primary-600" />
        <p class="text-gray-600 dark:text-gray-400">Loading issue details...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="!issue" class="flex justify-center items-center py-12">
      <div class="text-center">
        <UIcon name="lucide:alert-triangle" class="w-16 h-16 mx-auto mb-4 text-red-500" />
        <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">Issue Not Found</h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          The issue with ID "{{ issueId }}" could not be found.
        </p>
        <UButton @click="navigateTo('/issues')" icon="lucide:arrow-left">
          Back to Issues
        </UButton>
      </div>
    </div>

    <!-- Issue Detail Content -->
    <div v-else class="space-y-6">
      <!-- Page Header -->
      <div class="flex justify-between items-start">
        <div class="flex-1">
          <div class="flex items-center gap-3 mb-2">
            <UButton 
              variant="ghost" 
              icon="lucide:arrow-left" 
              @click="navigateTo('/issues')"
              class="shrink-0"
            >
              Back to Issues
            </UButton>
            <div class="flex items-center gap-2">
              <UBadge :color="getSeverityColor(issue.severity)" size="sm">
                {{ issue.severity }}
              </UBadge>
              <UBadge :color="getStatusColor(issue.status)" size="sm">
                {{ issue.status }}
              </UBadge>
              <UBadge variant="soft" size="sm">
                {{ issue.category }}
              </UBadge>
            </div>
          </div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1">
            {{ issue.title }}
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Issue #{{ issue.id }} • {{ getSiteName(issue.siteId) }} • 
            Created {{ formatDateTime(issue.timestamp) }}
          </p>
        </div>
        
        <div class="flex gap-2">
          <UDropdownMenu :items="actionMenuItems">
            <UButton variant="outline" icon="lucide:more-horizontal">
              Actions
            </UButton>
          </UDropdownMenu>
          <UButton 
            icon="lucide:edit" 
            @click="handleOpenEditModal"
            class="bg-primary-600 hover:bg-primary-700"
          >
            Edit Issue
          </UButton>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Issue Details -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Issue Information -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Issue Details
              </h3>
            </template>
            
            <div class="space-y-4">
              <!-- Description -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <p class="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
                    {{ issue.description }}
                  </p>
                </div>
              </div>

              <!-- Issue Metadata -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Severity
                  </label>
                  <UBadge :color="getSeverityColor(issue.severity)" size="sm">
                    {{ issue.severity }}
                  </UBadge>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Status
                  </label>
                  <UBadge :color="getStatusColor(issue.status)" size="sm">
                    {{ issue.status }}
                  </UBadge>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Category
                  </label>
                  <p class="text-sm text-gray-600 dark:text-gray-400">{{ issue.category }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Device ID
                  </label>
                  <p class="text-sm text-gray-600 dark:text-gray-400 font-mono">{{ issue.device_id }}</p>
                </div>
              </div>

              <!-- Site Information -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Site Information
                </label>
                <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <div class="flex items-center gap-3">
                    <UIcon name="lucide:building" class="w-5 h-5 text-gray-500" />
                    <div>
                      <p class="font-medium text-gray-900 dark:text-gray-100">
                        {{ getSiteName(issue.siteId) }}
                      </p>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        Site ID: {{ issue.siteId }}
                      </p>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ getSiteAddress(issue.siteId) }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </UCard>

          <!-- Photos Gallery -->
          <UCard v-if="issue.photos && issue.photos.length > 0">
            <template #header>
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Photos ({{ issue.photos.length }})
                </h3>
                <UButton 
                  variant="outline" 
                  icon="lucide:camera" 
                  size="sm"
                  @click="showAddPhotoModal = true"
                >
                  Add Photo
                </UButton>
              </div>
            </template>
            
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div 
                v-for="photo in issue.photos" 
                :key="photo.id"
                class="relative group cursor-pointer"
                @click="openPhotoModal(photo)"
              >
                <div class="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
                  <img 
                    :src="photo.uri" 
                    :alt="photo.title || 'Issue photo'"
                    class="w-full h-full object-cover transition-transform group-hover:scale-105"
                    @error="handleImageError"
                  />
                </div>
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                  <UIcon name="lucide:zoom-in" class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
                <div class="mt-2">
                  <p class="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {{ photo.title || `Photo ${photo.id.slice(-4)}` }}
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-500">
                    {{ formatDateTime(photo.timestamp) }}
                  </p>
                </div>
              </div>
            </div>
          </UCard>

          <!-- No Photos State -->
          <UCard v-else>
            <template #header>
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Photos
                </h3>
                <UButton 
                  variant="outline" 
                  icon="lucide:camera" 
                  size="sm"
                  @click="showAddPhotoModal = true"
                >
                  Add Photo
                </UButton>
              </div>
            </template>
            
            <div class="text-center py-8">
              <UIcon name="lucide:image" class="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p class="text-gray-600 dark:text-gray-400">No photos attached to this issue</p>
            </div>
          </UCard>
        </div>

        <!-- Right Column - Sidebar -->
        <div class="space-y-6">
          <!-- Quick Actions -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Quick Actions
              </h3>
            </template>
            
            <div class="space-y-3">
              <UButton 
                block 
                variant="outline" 
                icon="lucide:user-plus"
                @click="showAssignModal = true"
              >
                Assign Technician
              </UButton>
              <UButton 
                block 
                variant="outline" 
                icon="lucide:refresh-cw"
                @click="showStatusModal = true"
              >
                Update Status
              </UButton>
              <UButton 
                block 
                variant="outline" 
                icon="lucide:message-square"
                @click="showCommentModal = true"
              >
                Add Comment
              </UButton>
              <UButton 
                block 
                variant="outline" 
                icon="lucide:download"
                @click="handleExportIssue"
              >
                Export Issue
              </UButton>
            </div>
          </UCard>

          <!-- Issue Timeline -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Update History
              </h3>
            </template>
            
            <div class="space-y-4">
              <!-- Issue Created -->
              <div class="flex gap-3">
                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                  <UIcon name="lucide:plus-circle" class="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900 dark:text-gray-100">
                    Issue created
                  </p>
                  <p class="text-xs text-gray-500 dark:text-gray-500">
                    {{ formatDateTime(issue.timestamp) }}
                  </p>
                </div>
              </div>

              <!-- Updates from issue.updates -->
              <div 
                v-for="update in issue.updates || []" 
                :key="update.timestamp"
                class="flex gap-3"
              >
                <div class="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                  <UIcon name="lucide:edit" class="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-900 dark:text-gray-100">
                    {{ update.description }}
                  </p>
                  <div class="flex items-center gap-2 mt-1">
                    <p class="text-xs text-gray-500 dark:text-gray-500">
                      {{ formatDateTime(update.timestamp) }}
                    </p>
                    <span v-if="update.technician" class="text-xs text-gray-600 dark:text-gray-400">
                      by {{ update.technician }}
                    </span>
                  </div>
                  <div v-if="update.previousStatus && update.newStatus" class="mt-1 flex gap-2">
                    <UBadge :color="getStatusColor(update.previousStatus)" size="xs">
                      {{ update.previousStatus }}
                    </UBadge>
                    <UIcon name="lucide:arrow-right" class="w-3 h-3 text-gray-400" />
                    <UBadge :color="getStatusColor(update.newStatus)" size="xs">
                      {{ update.newStatus }}
                    </UBadge>
                  </div>
                </div>
              </div>

              <!-- No Updates State -->
              <div v-if="!issue.updates || issue.updates.length === 0" class="text-center py-4">
                <p class="text-sm text-gray-500 dark:text-gray-500">No updates yet</p>
              </div>
            </div>
          </UCard>

          <!-- Sync Status -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Sync Status
              </h3>
            </template>
            
            <div class="flex items-center gap-3">
              <div class="flex-shrink-0">
                <div 
                  :class="[
                    'w-3 h-3 rounded-full',
                    issue.sync_status === 'synced' ? 'bg-green-500' : 'bg-yellow-500'
                  ]"
                />
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                  {{ issue.sync_status }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-500">
                  {{ issue.sync_status === 'synced' ? 'All changes saved' : 'Pending sync' }}
                </p>
              </div>
            </div>
          </UCard>
        </div>
      </div>
    </div>

    <!-- Photo Modal -->
    <UModal v-model="showPhotoModal" :ui="{ wrapper: 'w-full sm:max-w-4xl' }">
      <UCard v-if="selectedPhoto">
        <template #header>
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold">
              {{ selectedPhoto.title || 'Issue Photo' }}
            </h3>
            <UButton 
              variant="ghost" 
              icon="lucide:x" 
              @click="showPhotoModal = false"
            />
          </div>
        </template>
        
        <div class="p-6">
          <img 
            :src="selectedPhoto.uri" 
            :alt="selectedPhoto.title || 'Issue photo'"
            class="w-full h-auto rounded-lg"
            @error="handleImageError"
          />
          <div class="mt-4 text-sm text-gray-600 dark:text-gray-400">
            <p>Taken: {{ formatDateTime(selectedPhoto.timestamp) }}</p>
            <p>Photo ID: {{ selectedPhoto.id }}</p>
          </div>
        </div>
      </UCard>
    </UModal>

    <!-- Edit Issue Modal -->
    <UModal v-model="showEditModal" :ui="{ wrapper: 'w-full sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Edit Issue
            </h3>
            <UButton 
              variant="ghost" 
              icon="lucide:x" 
              @click="handleCloseEditModal"
            />
          </div>
        </template>
        
        <div class="p-6 space-y-6">
          <!-- Issue Title -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Issue Title *
            </label>
            <UInput
              v-model="editForm.title"
              placeholder="Enter issue title..."
              size="lg"
              :error="!!formErrors.title"
              @blur="validateForm"
            />
            <p v-if="formErrors.title" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ formErrors.title }}
            </p>
          </div>

          <!-- Issue Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <UTextarea
              v-model="editForm.description"
              placeholder="Describe the issue in detail..."
              :rows="4"
              :error="!!formErrors.description"
              @blur="validateForm"
            />
            <p v-if="formErrors.description" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ formErrors.description }}
            </p>
          </div>

          <!-- Form Grid for Category, Site, Severity, Status -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Category Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category *
              </label>
              <USelectMenu
                v-model="editForm.category"
                :items="categoryOptions"
                value-key="value"
                placeholder="Select category"
                searchable
                :error="!!formErrors.category"
                @change="validateForm"
              >
                <template #leading>
                  <span v-if="editForm.category">{{ editForm.category }}</span>
                  <span v-else class="text-gray-500">Select category</span>
                </template>
              </USelectMenu>
              <p v-if="formErrors.category" class="mt-1 text-sm text-red-600 dark:text-red-400">
                {{ formErrors.category }}
              </p>
            </div>

            <!-- Site Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Site *
              </label>
              <USelectMenu
                v-model="editForm.siteId"
                :items="siteOptions"
                value-key="value"
                placeholder="Select site"
                searchable
                :error="!!formErrors.siteId"
                @change="validateForm"
              >
                <template #leading>
                  <span v-if="editForm.siteId">{{ getSiteName(editForm.siteId) }}</span>
                  <span v-else class="text-gray-500">Select site</span>
                </template>
              </USelectMenu>
              <p v-if="formErrors.siteId" class="mt-1 text-sm text-red-600 dark:text-red-400">
                {{ formErrors.siteId }}
              </p>
            </div>

            <!-- Severity Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Severity *
              </label>
              <USelectMenu
                v-model="editForm.severity"
                :items="severityOptions"
                value-key="value"
                placeholder="Select severity"
                :error="!!formErrors.severity"
                @change="validateForm"
              >
                <template #leading>
                  <span v-if="editForm.severity" class="flex items-center gap-2">
                    <UBadge :color="getSeverityColor(editForm.severity)" size="xs">
                      {{ editForm.severity }}
                    </UBadge>
                  </span>
                  <span v-else class="text-gray-500">Select severity</span>
                </template>
              </USelectMenu>
              <p v-if="formErrors.severity" class="mt-1 text-sm text-red-600 dark:text-red-400">
                {{ formErrors.severity }}
              </p>
            </div>

            <!-- Status Selection -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status *
              </label>
              <USelectMenu
                v-model="editForm.status"
                :items="statusOptions"
                value-key="value"
                placeholder="Select status"
                :error="!!formErrors.status"
                @change="validateForm"
              >
                <template #leading>
                  <span v-if="editForm.status" class="flex items-center gap-2">
                    <UBadge :color="getStatusColor(editForm.status)" size="xs">
                      {{ editForm.status }}
                    </UBadge>
                  </span>
                  <span v-else class="text-gray-500">Select status</span>
                </template>
              </USelectMenu>
              <p v-if="formErrors.status" class="mt-1 text-sm text-red-600 dark:text-red-400">
                {{ formErrors.status }}
              </p>
            </div>
          </div>

          <!-- Unsaved Changes Warning -->
          <div v-if="hasUnsavedChanges" class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div class="flex items-center gap-2">
              <UIcon name="lucide:alert-triangle" class="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <p class="text-sm text-yellow-700 dark:text-yellow-300">
                You have unsaved changes
              </p>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="flex justify-between items-center px-6 py-4">
            <!-- Form Validation Status -->
            <div class="flex items-center gap-2 text-sm">
              <UIcon 
                :name="isFormValid ? 'lucide:check-circle' : 'lucide:alert-circle'" 
                :class="[
                  'w-4 h-4',
                  isFormValid ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                ]"
              />
              <span :class="isFormValid ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
                {{ isFormValid ? 'Form is valid' : 'Please fix validation errors' }}
              </span>
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-3">
              <UButton 
                variant="ghost" 
                @click="handleCloseEditModal"
                :disabled="isSaving"
              >
                Cancel
              </UButton>
              <UButton 
                @click="handleSaveIssue"
                :loading="isSaving"
                :disabled="!isFormValid || isSaving"
                class="bg-primary-600 hover:bg-primary-700"
              >
                {{ isSaving ? 'Saving...' : 'Save Changes' }}
              </UButton>
            </div>
          </div>
        </template>
      </UCard>
    </UModal>

    <!-- Other Action Modals (Placeholders) -->
    <UModal v-model="showAssignModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Assign Technician</h3>
        </template>
        <div class="p-6 text-center">
          <p class="text-gray-600 dark:text-gray-400">Technician assignment will be implemented next.</p>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton variant="ghost" @click="showAssignModal = false">Cancel</UButton>
            <UButton @click="showAssignModal = false">Assign</UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <UModal v-model="showStatusModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Update Status</h3>
        </template>
        <div class="p-6 text-center">
          <p class="text-gray-600 dark:text-gray-400">Status update will be implemented next.</p>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton variant="ghost" @click="showStatusModal = false">Cancel</UButton>
            <UButton @click="showStatusModal = false">Update</UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <UModal v-model="showCommentModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Add Comment</h3>
        </template>
        <div class="p-6 text-center">
          <p class="text-gray-600 dark:text-gray-400">Comment system will be implemented next.</p>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton variant="ghost" @click="showCommentModal = false">Cancel</UButton>
            <UButton @click="showCommentModal = false">Add Comment</UButton>
          </div>
        </template>
      </UCard>
    </UModal>

    <UModal v-model="showAddPhotoModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">Add Photo</h3>
        </template>
        <div class="p-6 text-center">
          <p class="text-gray-600 dark:text-gray-400">Photo upload will be implemented next.</p>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <UButton variant="ghost" @click="showAddPhotoModal = false">Cancel</UButton>
            <UButton @click="showAddPhotoModal = false">Upload</UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { mockIssueReports, mockSites } from '~/data/mockData'
import type { IssueReport, IssueSeverity, IssueStatus, Photo, IssueCategory } from '~/types'
import { IssueCategory as IssueCategoryEnum, IssueSeverity as IssueSeverityEnum, IssueStatus as IssueStatusEnum } from '~/types'

// Get the issue ID from route params
const route = useRoute()
const issueId = route.params.id as string

// Use the issues composable for data management
const { updateIssue } = useIssues()

// Set page metadata using layout functions
const setPageTitle = inject('setPageTitle') as (title: string) => void
const setPageDescription = inject('setPageDescription') as (description: string) => void
const setBreadcrumbs = inject('setBreadcrumbs') as (crumbs: Array<{ label: string, to?: string }>) => void

// ================================
// STATE MANAGEMENT
// ================================

// Issue data
const issue = ref<IssueReport | null>(null)
const isLoading = ref(true)

// Modal states
const showPhotoModal = ref(false)
const showEditModal = ref(false)
const showAssignModal = ref(false)
const showStatusModal = ref(false)
const showCommentModal = ref(false)
const showAddPhotoModal = ref(false)

// Selected photo for modal
const selectedPhoto = ref<Photo | null>(null)

// Edit form state
const editForm = ref({
  title: '',
  description: '',
  category: undefined as IssueCategory | undefined,
  siteId: '',
  severity: undefined as IssueSeverity | undefined,
  status: undefined as IssueStatus | undefined
})

// Form validation and submission state
const isFormValid = ref(false)
const isSaving = ref(false)
const formErrors = ref<Record<string, string>>({})
const hasUnsavedChanges = ref(false)

// ================================
// COMPUTED PROPERTIES
// ================================

/**
 * Action menu items for the issue
 */
const actionMenuItems = computed(() => [
  [{
    label: 'Assign Technician',
    icon: 'lucide:user-plus',
    click: () => showAssignModal.value = true
  }],
  [{
    label: 'Update Status',
    icon: 'lucide:refresh-cw',
    click: () => showStatusModal.value = true
  }],
  [{
    label: 'Add Comment',
    icon: 'lucide:message-square',
    click: () => showCommentModal.value = true
  }],
  [{
    label: 'Duplicate Issue',
    icon: 'lucide:copy',
    click: () => handleDuplicateIssue()
  }],
  [{
    label: 'Delete Issue',
    icon: 'lucide:trash-2',
    click: () => handleDeleteIssue()
  }]
])

/**
 * Form options for dropdowns - following established patterns from issues list
 */
const severityOptions = computed(() => 
  Object.values(IssueSeverityEnum).map(severity => ({
    label: severity,
    value: severity
  }))
)

const statusOptions = computed(() => 
  Object.values(IssueStatusEnum).map(status => ({
    label: status,
    value: status
  }))
)

const categoryOptions = computed(() => 
  Object.values(IssueCategoryEnum).map(category => ({
    label: category,
    value: category
  }))
)

const siteOptions = computed(() => 
  mockSites.map(site => ({
    label: site.siteName,
    value: site.siteId
  }))
)

// ================================
// UTILITY FUNCTIONS
// ================================

/**
 * Get site name by ID
 */
const getSiteName = (siteId: string): string => {
  const site = mockSites.find(s => s.siteId === siteId)
  return site ? site.siteName : `Site ${siteId}`
}

/**
 * Get site address by ID
 */
const getSiteAddress = (siteId: string): string => {
  const site = mockSites.find(s => s.siteId === siteId)
  if (!site) return 'Address not available'
  
  const addr = site.siteAddress
  return `${addr.street}, ${addr.city}, ${addr.state} ${addr.country}`
}

/**
 * Get color for severity badge - must match UBadge color prop types
 */
const getSeverityColor = (severity: IssueSeverity): 'error' | 'warning' | 'success' | 'neutral' => {
  switch (severity) {
    case 'High': return 'error'
    case 'Medium': return 'warning'  
    case 'Low': return 'success'
    default: return 'neutral'
  }
}

/**
 * Get color for status badge - must match UBadge color prop types
 */
const getStatusColor = (status: IssueStatus): 'info' | 'secondary' | 'warning' | 'success' | 'neutral' => {
  switch (status) {
    case 'New': return 'info'
    case 'Assigned': return 'secondary'
    case 'In Progress': return 'warning'
    case 'Resolved': return 'success'
    default: return 'neutral'
  }
}

/**
 * Format date and time for display
 */
const formatDateTime = (timestamp: string): string => {
  return new Date(timestamp).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * Handle image loading errors
 */
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.src = '/placeholder-image.jpg' // Fallback image
  console.warn('Failed to load image:', target.src)
}

// ================================
// EVENT HANDLERS
// ================================

/**
 * Open photo in modal
 */
const openPhotoModal = (photo: Photo) => {
  selectedPhoto.value = photo
  showPhotoModal.value = true
}

/**
 * Handle export issue
 */
const handleExportIssue = () => {
  console.log('📄 Export issue:', issueId)
  // TODO: Implement export functionality
}

/**
 * Handle duplicate issue
 */
const handleDuplicateIssue = () => {
  console.log('📋 Duplicate issue:', issueId)
  // TODO: Implement duplicate functionality
}

/**
 * Handle delete issue
 */
const handleDeleteIssue = () => {
  console.log('🗑️ Delete issue:', issueId)
  // TODO: Implement delete functionality with confirmation
}

/**
 * Initialize edit form with current issue data
 */
const initializeEditForm = () => {
  if (!issue.value) return
  
  editForm.value = {
    title: issue.value.title,
    description: issue.value.description,
    category: issue.value.category,
    siteId: issue.value.siteId,
    severity: issue.value.severity,
    status: issue.value.status
  }
  
  // Reset form state
  formErrors.value = {}
  hasUnsavedChanges.value = false
  console.log('📝 Edit form initialized with current issue data')
}

/**
 * Validate form fields
 */
const validateForm = (): boolean => {
  formErrors.value = {}
  
  // Title validation
  if (!editForm.value.title.trim()) {
    formErrors.value.title = 'Title is required'
  } else if (editForm.value.title.trim().length < 3) {
    formErrors.value.title = 'Title must be at least 3 characters'
  }
  
  // Description validation
  if (!editForm.value.description.trim()) {
    formErrors.value.description = 'Description is required'
  } else if (editForm.value.description.trim().length < 10) {
    formErrors.value.description = 'Description must be at least 10 characters'
  }
  
  // Category validation
  if (!editForm.value.category) {
    formErrors.value.category = 'Category is required'
  }
  
  // Site validation
  if (!editForm.value.siteId.trim()) {
    formErrors.value.siteId = 'Site is required'
  }
  
  // Severity validation
  if (!editForm.value.severity) {
    formErrors.value.severity = 'Severity is required'
  }
  
  // Status validation
  if (!editForm.value.status) {
    formErrors.value.status = 'Status is required'
  }
  
  const isValid = Object.keys(formErrors.value).length === 0
  isFormValid.value = isValid
  
  console.log('🔍 Form validation result:', isValid ? '✅ Valid' : '❌ Invalid', formErrors.value)
  return isValid
}

/**
 * Handle form submission
 */
const handleSaveIssue = async () => {
  if (!issue.value || !validateForm()) {
    console.warn('⚠️ Form validation failed, cannot save')
    return
  }
  
  try {
    isSaving.value = true
    console.log('💾 Saving issue changes:', editForm.value)
    
    // Prepare update data
    const updateData: Partial<IssueReport> = {
      title: editForm.value.title.trim(),
      description: editForm.value.description.trim(),
      category: editForm.value.category!,
      siteId: editForm.value.siteId.trim(),
      severity: editForm.value.severity!,
      status: editForm.value.status!
    }
    
    // Use the updateIssue function from useIssues composable
    const result = await updateIssue(issueId, updateData)
    
    if (result.success && result.data) {
      // Update local issue data with the result
      issue.value = result.data
      
      // Show success feedback
      console.log('✅ Issue saved successfully:', result.data.id)
      
      // Close modal and reset state
      showEditModal.value = false
      hasUnsavedChanges.value = false
      
      // Update page metadata with new title
      setPageTitle(`Issue: ${issue.value.title}`)
      setPageDescription(`${issue.value.severity} severity ${issue.value.category} issue at ${getSiteName(issue.value.siteId)}`)
      
      // TODO: Add success toast notification
      console.log('📝 Issue details updated successfully')
      
    } else {
      // Handle update failure
      console.error('❌ Failed to save issue:', result.error)
      
      // TODO: Show error toast notification
      console.error('Failed to save changes:', result.error)
    }
    
  } catch (error) {
    console.error('❌ Exception while saving issue:', error)
    
    // TODO: Show error toast notification
    console.error('An error occurred while saving:', error)
  } finally {
    isSaving.value = false
  }
}

/**
 * Handle edit modal opening
 */
const handleOpenEditModal = () => {
  initializeEditForm()
  showEditModal.value = true
}

/**
 * Handle edit modal closing with unsaved changes check
 */
const handleCloseEditModal = () => {
  if (hasUnsavedChanges.value) {
    // TODO: Add confirmation dialog for unsaved changes
    const shouldClose = confirm('You have unsaved changes. Are you sure you want to close?')
    if (!shouldClose) return
  }
  
  showEditModal.value = false
  hasUnsavedChanges.value = false
  formErrors.value = {}
}

/**
 * Watch for form changes to detect unsaved changes
 */
watch(editForm, () => {
  hasUnsavedChanges.value = true
}, { deep: true })

// ================================
// DATA LOADING
// ================================

/**
 * Load issue data
 */
const loadIssue = async () => {
  try {
    isLoading.value = true
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Find issue in mock data
    const foundIssue = mockIssueReports.find(i => i.id === issueId)
    
    if (foundIssue) {
      issue.value = foundIssue
      
      // Update page metadata
      setPageTitle(`Issue: ${foundIssue.title}`)
      setPageDescription(`${foundIssue.severity} severity ${foundIssue.category} issue at ${getSiteName(foundIssue.siteId)}`)
      setBreadcrumbs([
        { label: 'Issues', to: '/issues' },
        { label: foundIssue.title }
      ])
      
      console.log('📋 Loaded issue details:', foundIssue.id)
    } else {
      console.warn('❌ Issue not found:', issueId)
      
      // Set fallback metadata
      setPageTitle('Issue Not Found')
      setPageDescription('The requested issue could not be found')
      setBreadcrumbs([
        { label: 'Issues', to: '/issues' },
        { label: 'Not Found' }
      ])
    }
  } catch (error) {
    console.error('Error loading issue:', error)
  } finally {
    isLoading.value = false
  }
}

// ================================
// LIFECYCLE HOOKS
// ================================

/**
 * Initialize page data on mount
 */
onMounted(async () => {
  console.log('🔍 Loading issue with ID:', issueId)
  await loadIssue()
})

/**
 * Watch for route changes to reload issue
 */
watch(() => route.params.id, async (newId) => {
  if (newId && newId !== issueId) {
    console.log('🔄 Route changed, loading new issue:', newId)
    await loadIssue()
  }
})
</script>
# Desktop Admin PRD - Implementation Plan

## Mobile App Architecture Analysis

### 1. Project Structure & Architecture
- **Framework**: React Native with Expo (v53.0.9)
- **Navigation**: Expo Router with tab-based navigation
- **UI Framework**: NativeWind (Tailwind CSS for React Native)
- **State Management**: React Context API (IssueContext)
- **Backend**: Supabase integration for data sync
- **Storage**: Hybrid architecture (local + cloud sync)

### 2. Core Features & Functionality

#### **Main Screens:**
1. **Dashboard/Home** (`index.tsx`):
   - Statistics cards showing issue counts by severity and status
   - Recent activity feed
   - Visual charts (bar charts for issue distribution)
   - Quick navigation to other sections

2. **Issues List** (`issues.tsx`):
   - Filterable list of all issues
   - Search and filter capabilities (by severity, status)
   - Issue cards with key information
   - Pull-to-refresh functionality

3. **Create Issue** (`create-issue.tsx`):
   - Form with fields: title, category, site, description, severity, status
   - Photo attachment capability (camera + gallery)
   - Form validation and error handling
   - Site selector with search functionality

4. **Issue Detail** (`issue/[id].tsx`):
   - Full issue information display
   - Photo gallery with full-screen view
   - Update history with photos
   - Edit/delete capabilities

#### **Data Models:**
- **Issue**: ID, title, category, description, siteId, severity, status, photos, timestamp, sync_status
- **Photo**: ID, URI, timestamp, title
- **Site**: siteId, siteName, address, status
- **Categories**: Docket, Vandalism, Corrective, Preventive, Audit
- **Severities**: Low, Medium, High
- **Statuses**: New, Assigned, In Progress, Resolved

#### **Key Services:**
- **IssueRepository**: CRUD operations for issues
- **SyncService**: Supabase synchronization
- **StorageService**: Local storage management
- **CameraService**: Photo capture and management
- **SiteService**: Site data management

### 3. Business Logic & Workflows
- **Offline-first architecture** with background sync
- **Device isolation** using UUID-based device_id
- **Photo management** with compression and optimization
- **Real-time sync** to Supabase when online
- **Form validation** with comprehensive error handling

## Desktop Admin Interface Plan
This plan will create a powerful desktop admin interface that complements the mobile app, providing comprehensive management capabilities while maintaining the same data models and business logic.

## ✅ COMPLETED: Basic Dashboard Foundation

### Overview
✅ **COMPLETED** - Comprehensive admin dashboard UI using Nuxt UI components with mock data that mirrors the mobile app's functionality but optimized for desktop usage.

### ✅ Completed Implementation Steps

#### ✅ 1. Dashboard Layout & Structure
- **✅ Created admin layout** (`app/layouts/default.vue`) with sidebar navigation and header
- **✅ Designed responsive grid system** for dashboard cards and content
- **✅ Implemented navigation sidebar** with menu items (Dashboard, Issues, Sites, Users, Reports, Settings)
- **✅ Added header** with user profile, notifications, and breadcrumbs

#### ✅ 2. Statistics Cards Section
- **✅ Created statistics cards** showing:
  - Total issues count
  - Issues by severity (High, Medium, Low)
  - Issues by status (New, In Progress, Resolved)
  - Active sites and users
- **✅ Used Nuxt UI Card components** with icons and color coding
- **✅ Added hover effects** and responsive design

#### ✅ 3. Recent Activity Feed
- **✅ Designed activity timeline** showing recent issue updates
- **✅ Included activity types**: Created, Updated, Resolved, Assigned
- **✅ Shows user info**, timestamps, and severity badges
- **✅ Used Nuxt UI Avatar** and Badge components

#### ✅ 4. Data Visualization
- **✅ Implemented visual charts** for issue distribution using progress bars
- **✅ Created status and severity distribution** with visual indicators
- **✅ Added responsive chart sizing** with proper color coding

#### ✅ 5. Navigation & User Experience
- **✅ Added notification system** with slide-out panel
- **✅ Implemented user dropdown menu** with profile and settings
- **✅ Created breadcrumb navigation** system
- **✅ Added proper routing** structure

#### ✅ 6. Mock Data Structure
- **✅ Created TypeScript interfaces** (`app/types/index.ts`) matching mobile app data models
- **✅ Generated realistic mock data** (`app/data/mockData.ts`) for issues, sites, users, and activities
- **✅ Ensured data consistency** across all dashboard components

#### ✅ 7. Desktop Optimizations
- **✅ Optimized for larger screens** with multi-column layouts
- **✅ Added desktop-specific UI patterns** (hover states, transitions)
- **✅ Implemented responsive design** for various screen sizes
- **✅ Used proper sidebar and header layout** for desktop admin interface

### ✅ Technical Implementation Completed
- **✅ Used Nuxt UI components**: UCard, UTable, UButton, UBadge, UAvatar, UIcon, UDropdownMenu
- **✅ Implemented with TypeScript** for complete type safety
- **✅ Added comprehensive comments** explaining each component's functionality
- **✅ Followed responsive design principles** for various screen sizes
- **✅ Used Tailwind CSS classes** for consistent styling

---

## 🚀 NEXT IMPLEMENTATION: Issues Management Interface

### Overview
Create a comprehensive Issues List page that serves as the primary interface for managing all maintenance issues, with advanced filtering, search, and bulk operations capabilities.

### Implementation Goals

#### 1. Issues List Page (`app/pages/issues/index.vue`)
- **Create main issues table** using Nuxt UI Table component
- **Display all issue fields**: title, site, severity, status, assigned user, dates
- **Add proper column sorting** by date, severity, status, site
- **Include row actions** (view, edit, delete, assign)
- **Add issue status indicators** with color coding

#### 2. Advanced Filtering System
- **Filter by severity**: High, Medium, Low with color-coded badges
- **Filter by status**: New, In Progress, Resolved, Closed
- **Filter by site**: Dropdown with all available sites
- **Filter by date range**: Created date, updated date, resolved date
- **Filter by assigned user**: Show issues assigned to specific technicians
- **Clear all filters** functionality

#### 3. Search Functionality
- **Global search** across title, description, site name
- **Real-time search** with debounced input
- **Search highlighting** in results
- **Search history** for recent searches

#### 4. Bulk Operations
- **Multi-select checkboxes** for issues
- **Bulk status updates** (assign, resolve, close)
- **Bulk assignment** to users
- **Bulk export** functionality
- **Select all/none** functionality

#### 5. Create Issue Integration
- **"Create Issue" button** prominently displayed
- **Modal or page navigation** to issue creation form
- **Quick create** functionality for common issue types

#### 6. Pagination & Performance
- **Server-side pagination** (simulated with mock data)
- **Configurable page sizes** (25, 50, 100 items)
- **Loading states** and skeleton loaders
- **Optimistic updates** for better UX

#### 7. Issue Detail Integration
- **Click-to-view** issue details in modal or separate page
- **Quick preview** on hover
- **Edit in place** for simple fields
- **Navigation breadcrumbs** for issue detail pages

### Technical Implementation Details

#### **Files to Create:**
- `app/pages/issues/index.vue` - Main issues list page
- `app/pages/issues/[id].vue` - Individual issue detail page
- `app/components/IssueTable.vue` - Reusable issues table component
- `app/components/IssueFilters.vue` - Filter components
- `app/components/IssueActions.vue` - Bulk actions component
- `app/composables/useIssues.ts` - Issues data management composable

#### **Key Features:**
- **Responsive table** that works on all screen sizes
- **Keyboard navigation** for accessibility
- **Export functionality** (CSV, PDF)
- **Real-time updates** simulation
- **Error handling** and loading states
- **TypeScript throughout** with proper interfaces

#### **Data Integration:**
- **Use existing mock data** from `mockIssues`
- **Implement filtering logic** in composable
- **Add pagination helpers** 
- **Create search utilities**
- **Handle state management** for filters and selections

This implementation will provide a solid foundation for issue management and demonstrate advanced table functionality that can be replicated for Sites, Users, and other list pages.

### Phase 1: Core Architecture Setup
1. **Configure Nuxt 3 project** with TypeScript and Nuxt UI
2. **Set up data models** matching the mobile app's TypeScript interfaces
3. **Create Supabase client** for direct database access
4. **Implement authentication** (admin-level access)
5. **Set up routing structure** with file-based pages

### Phase 2: Dashboard Implementation
1. **Admin Dashboard**:
   - Overview statistics (total issues, by severity, by status, by site)
   - Recent activity feed across all devices
   - Visual charts and graphs
   - Quick action buttons

2. **Issue Management**:
   - Comprehensive issue list with advanced filtering
   - Bulk operations (status updates, assignments)
   - Search functionality across all fields
   - Export capabilities

### Phase 3: Issue Detail & Management
1. **Issue Detail Pages**:
   - Full issue information with edit capabilities
   - Photo gallery with management tools
   - Update history and audit trail
   - Comments/notes system

2. **Issue Forms**:
   - Create new issues from admin interface
   - Edit existing issues
   - Bulk edit capabilities
   - Status workflow management

### Phase 4: Site & User Management
1. **Site Management**:
   - Site creation and editing
   - Site status management
   - Geographic information
   - Site-specific reporting

2. **User/Device Management**:
   - Device tracking and management
   - User activity monitoring
   - Permission management

### Phase 5: Advanced Features
1. **Reporting & Analytics**:
   - Custom reports and dashboards
   - Data visualization
   - Export capabilities
   - Trend analysis

2. **System Administration**:
   - Sync monitoring
   - Data integrity checks
   - System health dashboard
   - Configuration management

### Technical Implementation Details

#### **Technology Stack:**
- **Frontend**: Nuxt 3 with TypeScript
- **UI**: Nuxt UI components (consistent with project preferences)
- **Database**: Direct Supabase connection
- **Authentication**: Supabase Auth
- **State Management**: Pinia (Nuxt/Vue state management)

#### **Key Components to Create:**
1. **Layouts**: Admin layout with navigation
2. **Pages**: Dashboard, issues, sites, users, reports
3. **Components**: Data tables, forms, charts, photo galleries
4. **Composables**: Data fetching, state management
5. **Utils**: Date formatting, validation, export functions

#### **Data Flow:**
- Direct Supabase database access (no device_id filtering for admin)
- Real-time subscriptions for live updates
- Optimistic updates for better UX
- Comprehensive error handling

### Memory Notes
- Always refer to my approach from @app/pages/issues/ for typescript integration with Nuxt UI components.
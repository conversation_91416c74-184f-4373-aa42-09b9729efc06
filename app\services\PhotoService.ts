/**
 * Photo upload/management service class
 * Handles business logic for photo operations with Supabase Storage
 * Provides comprehensive photo management for the desktop admin interface
 */

import type { Photo } from '~/types'

export class PhotoService {
  private supabase: any
  private readonly BUCKET_NAME = 'report-photos'
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  private readonly SUPPORTED_FORMATS = ['image/jpeg', 'image/png', 'image/webp']
  private readonly IMAGE_QUALITY = 0.85
  private readonly MAX_WIDTH = 1920
  private readonly MAX_HEIGHT = 1080
  
  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  /**
   * Get photo statistics for dashboard
   * Returns aggregated data for admin overview
   */
  async getPhotoStatistics(): Promise<{
    success: boolean
    data?: {
      totalPhotos: number
      totalStorageUsed: number
      photosByIssue: number
      recentUploads: number
      storageBreakdown: {
        images: number
        thumbnails: number
        compressed: number
      }
    }
    error?: string
  }> {
    try {
      console.log('Fetching photo statistics...')
      
      // Get storage bucket stats
      const { data: bucketStats, error: bucketError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .list('', {
          limit: 1000,
          sortBy: { column: 'created_at', order: 'desc' }
        })
      
      if (bucketError) {
        return { success: false, error: bucketError.message }
      }
      
      let totalPhotos = 0
      let totalStorageUsed = 0
      let recentUploads = 0
      
      const weekAgo = new Date()
      weekAgo.setDate(weekAgo.getDate() - 7)
      
      bucketStats?.forEach((file: any) => {
        totalPhotos++
        totalStorageUsed += file.metadata?.size || 0
        
        if (new Date(file.created_at) > weekAgo) {
          recentUploads++
        }
      })
      
      // Get issue count with photos
      const { data: issuesWithPhotos, error: issuesError } = await this.supabase
        .from('reports')
        .select('id')
        .gt('photos->0', 'null') // Issues that have photos array with at least one item
      
      const photosByIssue = issuesWithPhotos?.length || 0
      
      console.log('✅ Photo statistics fetched successfully')
      
      return {
        success: true,
        data: {
          totalPhotos,
          totalStorageUsed,
          photosByIssue,
          recentUploads,
          storageBreakdown: {
            images: Math.round(totalStorageUsed * 0.7),
            thumbnails: Math.round(totalStorageUsed * 0.2),
            compressed: Math.round(totalStorageUsed * 0.1)
          }
        }
      }
    } catch (err) {
      console.error('Error fetching photo statistics:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch photo statistics' 
      }
    }
  }

  /**
   * Upload photo with validation and optimization
   * Handles image processing and storage management
   */
  async uploadPhotoWithProcessing(
    file: File,
    issueId: string,
    title?: string
  ): Promise<{
    success: boolean
    data?: Photo
    error?: string
    metadata?: {
      originalSize: number
      compressedSize: number
      compressionRatio: number
    }
  }> {
    try {
      console.log('Uploading photo with processing:', file.name)
      
      // Validate file
      const validation = this.validatePhotoFile(file)
      if (!validation.isValid) {
        return { success: false, error: validation.error }
      }
      
      // Generate unique photo ID and path
      const photoId = this.generatePhotoId()
      const originalSize = file.size
      
      // Process image (compress, resize, optimize)
      const processedFile = await this.processImage(file)
      const compressedSize = processedFile.size
      const compressionRatio = Math.round((1 - compressedSize / originalSize) * 100)
      
      // Upload to Supabase Storage
      const uploadResult = await this.uploadToStorage(processedFile, issueId, photoId)
      if (!uploadResult.success) {
        return { success: false, error: uploadResult.error }
      }
      
      // Create photo record
      const photo: Photo = {
        id: photoId,
        uri: uploadResult.publicUrl!,
        timestamp: new Date().toISOString(),
        title: title || file.name
      }
      
      // Update issue with photo reference (if needed)
      await this.updateIssuePhotoReference(issueId, photo)
      
      console.log('✅ Photo uploaded and processed successfully:', photoId)
      
      return {
        success: true,
        data: photo,
        metadata: {
          originalSize,
          compressedSize,
          compressionRatio
        }
      }
    } catch (err) {
      console.error('Exception uploading photo:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to upload photo' 
      }
    }
  }

  /**
   * Bulk upload photos with progress tracking
   * Handles multiple file uploads efficiently
   */
  async bulkUploadPhotos(
    files: File[],
    issueId: string,
    onProgress?: (completed: number, total: number, currentFile: string) => void
  ): Promise<{
    success: boolean
    data?: Photo[]
    error?: string
    summary?: {
      totalFiles: number
      successful: number
      failed: number
      totalSizeOriginal: number
      totalSizeCompressed: number
      averageCompressionRatio: number
    }
  }> {
    try {
      console.log(`Starting bulk upload of ${files.length} photos`)
      
      const results: Photo[] = []
      const failures: string[] = []
      let totalOriginalSize = 0
      let totalCompressedSize = 0
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        onProgress?.(i, files.length, file.name)
        
        const uploadResult = await this.uploadPhotoWithProcessing(file, issueId)
        
        if (uploadResult.success && uploadResult.data) {
          results.push(uploadResult.data)
          totalOriginalSize += uploadResult.metadata?.originalSize || 0
          totalCompressedSize += uploadResult.metadata?.compressedSize || 0
        } else {
          failures.push(`${file.name}: ${uploadResult.error}`)
        }
      }
      
      onProgress?.(files.length, files.length, 'Complete')
      
      const averageCompressionRatio = totalOriginalSize > 0 
        ? Math.round((1 - totalCompressedSize / totalOriginalSize) * 100)
        : 0
      
      console.log(`✅ Bulk upload completed: ${results.length} successful, ${failures.length} failed`)
      
      return {
        success: failures.length < files.length, // Success if at least one upload succeeded
        data: results,
        error: failures.length > 0 ? `Some uploads failed: ${failures.join(', ')}` : undefined,
        summary: {
          totalFiles: files.length,
          successful: results.length,
          failed: failures.length,
          totalSizeOriginal: totalOriginalSize,
          totalSizeCompressed: totalCompressedSize,
          averageCompressionRatio
        }
      }
    } catch (err) {
      console.error('Exception during bulk upload:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Bulk upload failed' 
      }
    }
  }

  /**
   * Delete photo from storage and update references
   * Handles cleanup of storage and database references
   */
  async deletePhotoWithCleanup(
    photoId: string,
    issueId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Deleting photo with cleanup:', photoId)
      
      // Find and delete from storage
      const deleteResult = await this.deleteFromStorage(photoId, issueId)
      if (!deleteResult.success) {
        return { success: false, error: deleteResult.error }
      }
      
      // Update issue to remove photo reference
      await this.removeIssuePhotoReference(issueId, photoId)
      
      console.log('✅ Photo deleted successfully:', photoId)
      return { success: true }
    } catch (err) {
      console.error('Exception deleting photo:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to delete photo' 
      }
    }
  }

  /**
   * Generate optimized photo thumbnails
   * Creates smaller versions for list views
   */
  async generateThumbnail(
    photoId: string,
    issueId: string,
    size: number = 150
  ): Promise<{ success: boolean; thumbnailUrl?: string; error?: string }> {
    try {
      console.log('Generating thumbnail for photo:', photoId)
      
      // Get original photo
      const { data: originalFile, error: downloadError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .download(`${issueId}/${photoId}.jpg`)
      
      if (downloadError) {
        return { success: false, error: downloadError.message }
      }
      
      // Create thumbnail
      const thumbnailFile = await this.createThumbnail(originalFile, size)
      
      // Upload thumbnail
      const thumbnailPath = `${issueId}/thumbnails/${photoId}_thumb.jpg`
      const { error: uploadError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .upload(thumbnailPath, thumbnailFile, {
          cacheControl: '86400', // 24 hours
          upsert: true
        })
      
      if (uploadError) {
        return { success: false, error: uploadError.message }
      }
      
      // Get public URL
      const { data: { publicUrl } } = this.supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(thumbnailPath)
      
      console.log('✅ Thumbnail generated successfully')
      return { success: true, thumbnailUrl: publicUrl }
    } catch (err) {
      console.error('Exception generating thumbnail:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to generate thumbnail' 
      }
    }
  }

  /**
   * Validate photo file before upload
   */
  private validatePhotoFile(file: File): { isValid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File size too large. Maximum size is ${Math.round(this.MAX_FILE_SIZE / 1024 / 1024)}MB`
      }
    }
    
    // Check file format
    if (!this.SUPPORTED_FORMATS.includes(file.type)) {
      return {
        isValid: false,
        error: `Unsupported file format. Supported formats: ${this.SUPPORTED_FORMATS.join(', ')}`
      }
    }
    
    // Check if it's actually an image
    if (!file.type.startsWith('image/')) {
      return {
        isValid: false,
        error: 'File must be an image'
      }
    }
    
    return { isValid: true }
  }

  /**
   * Process image for optimal storage
   */
  private async processImage(file: File): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // Calculate optimal dimensions
        let { width, height } = img
        
        if (width > this.MAX_WIDTH || height > this.MAX_HEIGHT) {
          const ratio = Math.min(this.MAX_WIDTH / width, this.MAX_HEIGHT / height)
          width = Math.round(width * ratio)
          height = Math.round(height * ratio)
        }
        
        canvas.width = width
        canvas.height = height
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const processedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              })
              resolve(processedFile)
            } else {
              resolve(file) // Fallback
            }
          },
          'image/jpeg',
          this.IMAGE_QUALITY
        )
      }
      
      img.onerror = () => resolve(file)
      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Create thumbnail from original image
   */
  private async createThumbnail(originalBlob: Blob, size: number): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()
      
      img.onload = () => {
        // Calculate square thumbnail dimensions
        const minDimension = Math.min(img.width, img.height)
        const cropX = (img.width - minDimension) / 2
        const cropY = (img.height - minDimension) / 2
        
        canvas.width = size
        canvas.height = size
        
        // Draw cropped and resized image
        ctx?.drawImage(
          img,
          cropX, cropY, minDimension, minDimension,
          0, 0, size, size
        )
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const thumbnailFile = new File([blob], 'thumbnail.jpg', {
                type: 'image/jpeg',
                lastModified: Date.now()
              })
              resolve(thumbnailFile)
            } else {
              // Fallback: create a simple file from original blob
              resolve(new File([originalBlob], 'thumbnail.jpg', { type: 'image/jpeg' }))
            }
          },
          'image/jpeg',
          0.8
        )
      }
      
      img.onerror = () => {
        resolve(new File([originalBlob], 'thumbnail.jpg', { type: 'image/jpeg' }))
      }
      
      img.src = URL.createObjectURL(originalBlob)
    })
  }

  /**
   * Upload file to Supabase Storage
   */
  private async uploadToStorage(
    file: File,
    issueId: string,
    photoId: string
  ): Promise<{ success: boolean; publicUrl?: string; error?: string }> {
    try {
      const filePath = `${issueId}/${photoId}.jpg`
      
      const { error: uploadError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })
      
      if (uploadError) {
        return { success: false, error: uploadError.message }
      }
      
      const { data: { publicUrl } } = this.supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath)
      
      return { success: true, publicUrl }
    } catch (err) {
      return { success: false, error: 'Upload to storage failed' }
    }
  }

  /**
   * Delete file from Supabase Storage
   */
  private async deleteFromStorage(
    photoId: string,
    issueId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const filePath = `${issueId}/${photoId}.jpg`
      const thumbnailPath = `${issueId}/thumbnails/${photoId}_thumb.jpg`
      
      // Delete main image
      const { error: deleteError } = await this.supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath])
      
      // Delete thumbnail (ignore errors)
      await this.supabase.storage
        .from(this.BUCKET_NAME)
        .remove([thumbnailPath])
      
      if (deleteError) {
        return { success: false, error: deleteError.message }
      }
      
      return { success: true }
    } catch (err) {
      return { success: false, error: 'Delete from storage failed' }
    }
  }

  /**
   * Update issue with photo reference
   */
  private async updateIssuePhotoReference(issueId: string, photo: Photo): Promise<void> {
    try {
      // In a real implementation, this might update a photos array in the issue record
      // For now, we'll just log the action
      console.log(`Updated issue ${issueId} with photo reference:`, photo.id)
    } catch (err) {
      console.warn('Failed to update issue photo reference:', err)
    }
  }

  /**
   * Remove photo reference from issue
   */
  private async removeIssuePhotoReference(issueId: string, photoId: string): Promise<void> {
    try {
      // In a real implementation, this might remove the photo from the issue's photos array
      console.log(`Removed photo ${photoId} reference from issue ${issueId}`)
    } catch (err) {
      console.warn('Failed to remove issue photo reference:', err)
    }
  }

  /**
   * Generate unique photo ID
   */
  private generatePhotoId(): string {
    return `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}
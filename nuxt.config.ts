// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  // @ts-ignore - Supabase module types not fully integrated
  future: {
    compatibilityVersion: 4,
  },
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },

  modules: [
    '@nuxt/eslint',
    '@nuxt/fonts',
    '@nuxt/icon',
    '@nuxt/image',
    '@nuxt/ui',
    '@nuxtjs/supabase'
  ],

  css: ['~/assets/css/main.css'],

  // Supabase configuration
  supabase: {
    redirectOptions: {
      login: '/',
      callback: '/',
      exclude: ['/', '/issues', '/sites', '/users', '/reports', '/settings', '/activities']
    }
  },

  // Runtime configuration
  runtimeConfig: {
    public: {
      supabase: {
        url: process.env.SUPABASE_URL,
        key: process.env.SUPABASE_ANON_KEY
      }
    }
  }
})
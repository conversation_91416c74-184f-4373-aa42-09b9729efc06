/**
 * Site management operations service class
 * Handles business logic for site management with admin privileges
 * Provides comprehensive site operations for the desktop admin interface
 */

import type { Site, SiteAddress, SiteOption } from '~/types'
import { SiteStatus } from '~/types'

export class SiteService {
  private supabase: any
  
  constructor(supabaseClient: any) {
    this.supabase = supabaseClient
  }

  /**
   * Get site statistics for dashboard
   * Returns aggregated data for admin overview
   */
  async getSiteStatistics(): Promise<{
    success: boolean
    data?: {
      total: number
      active: number
      byStatus: Record<SiteStatus, number>
      recentlyAdded: number
      topSitesByIssues: Array<{ site: Site; issueCount: number }>
    }
    error?: string
  }> {
    try {
      console.log('Fetching site statistics...')
      
      // Get all sites
      const { data: allSites, error: sitesError } = await this.supabase
        .from('sites')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (sitesError) {
        return { success: false, error: sitesError.message }
      }
      
      // Aggregate site data
      const byStatus: Record<SiteStatus, number> = {
        [SiteStatus.Active]: 0,
        [SiteStatus.Support]: 0,
        [SiteStatus.NotActive]: 0,
        [SiteStatus.Dismantled]: 0
      }
      
      let activeCount = 0
      let recentlyAdded = 0
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      allSites?.forEach((site: any) => {
        // Count by status
        if (site.status in byStatus) {
          byStatus[site.status as SiteStatus]++
        }
        
        // Count active sites
        if (site.status === SiteStatus.Active) {
          activeCount++
        }
        
        // Count recently added sites
        if (new Date(site.created_at) > thirtyDaysAgo) {
          recentlyAdded++
        }
      })
      
      // Get sites with most issues
      const { data: issueStats, error: issueStatsError } = await this.supabase
        .from('reports')
        .select('location')
        .then((result: any) => {
          if (result.error) return result
          
          // Count issues by site
          const issueCountsBySite: Record<string, number> = {}
          result.data?.forEach((report: any) => {
            issueCountsBySite[report.location] = (issueCountsBySite[report.location] || 0) + 1
          })
          
          return { data: issueCountsBySite, error: null }
        })
      
      if (issueStatsError) {
        console.warn('Could not fetch issue statistics for sites:', issueStatsError)
      }
      
      // Get top sites by issues
      const topSitesByIssues: Array<{ site: Site; issueCount: number }> = []
      if (issueStats.data && allSites) {
        const siteMap = new Map(allSites.map((site: any) => [site.site_id, this.transformDbRecordToSite(site)]))
        
        Object.entries(issueStats.data)
          .sort(([, a], [, b]) => (b as number) - (a as number))
          .slice(0, 5)
          .forEach(([siteId, count]) => {
            const site = siteMap.get(siteId)
            if (site) {
              topSitesByIssues.push({ site, issueCount: count as number })
            }
          })
      }
      
      console.log('✅ Site statistics fetched successfully')
      
      return {
        success: true,
        data: {
          total: allSites?.length || 0,
          active: activeCount,
          byStatus,
          recentlyAdded,
          topSitesByIssues
        }
      }
    } catch (err) {
      console.error('Error fetching site statistics:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch site statistics' 
      }
    }
  }

  /**
   * Create site with validation and geocoding
   * Handles proper data validation and address geocoding
   */
  async createSiteWithValidation(siteData: Omit<Site, 'siteId'>): Promise<{
    success: boolean
    data?: Site
    error?: string
  }> {
    try {
      console.log('Creating site with validation:', siteData.siteName)
      
      // Validate required fields
      const validation = this.validateSiteInput(siteData)
      if (!validation.isValid) {
        return { success: false, error: validation.error }
      }
      
      // Check for duplicate site names
      const duplicateCheck = await this.checkDuplicateSiteName(siteData.siteName)
      if (!duplicateCheck.success) {
        return { success: false, error: duplicateCheck.error }
      }
      
      // Generate unique site ID
      const siteId = this.generateSiteId(siteData.siteName)
      
      // Validate and potentially enhance address data
      const validatedAddress = await this.validateAndEnhanceAddress(siteData.siteAddress)
      
      // Prepare site data for database
      const newSiteData = {
        site_id: siteId,
        site_name: siteData.siteName.trim(),
        site_address: validatedAddress,
        status: siteData.status || SiteStatus.Active,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      // Insert to database
      const { data, error } = await this.supabase
        .from('sites')
        .insert([newSiteData])
        .select()
        .single()
      
      if (error) {
        console.error('Database error creating site:', error)
        return { success: false, error: error.message }
      }
      
      // Transform to Site format
      const createdSite = this.transformDbRecordToSite(data)
      
      console.log('✅ Site created successfully:', siteId)
      return { success: true, data: createdSite }
    } catch (err) {
      console.error('Exception creating site:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create site' 
      }
    }
  }

  /**
   * Update site status with validation
   * Ensures proper status transitions and business rules
   */
  async updateSiteStatus(
    siteId: string, 
    newStatus: SiteStatus, 
    notes?: string
  ): Promise<{ success: boolean; data?: Site; error?: string }> {
    try {
      console.log(`Updating site ${siteId} status to ${newStatus}`)
      
      // Get current site
      const { data: currentSite, error: fetchError } = await this.supabase
        .from('sites')
        .select('*')
        .eq('site_id', siteId)
        .single()
      
      if (fetchError) {
        return { success: false, error: fetchError.message }
      }
      
      // Validate status transition
      const isValidTransition = this.validateStatusTransition(currentSite.status, newStatus)
      if (!isValidTransition) {
        return { 
          success: false, 
          error: `Invalid status transition from ${currentSite.status} to ${newStatus}` 
        }
      }
      
      // Check business rules
      if (newStatus === SiteStatus.Dismantled) {
        const hasActiveIssues = await this.checkActiveIssues(siteId)
        if (hasActiveIssues) {
          return { 
            success: false, 
            error: 'Cannot dismantle site with active issues. Please resolve all issues first.' 
          }
        }
      }
      
      // Update site
      const { data, error } = await this.supabase
        .from('sites')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('site_id', siteId)
        .select()
        .single()
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      const updatedSite = this.transformDbRecordToSite(data)
      
      console.log('✅ Site status updated successfully')
      return { success: true, data: updatedSite }
    } catch (err) {
      console.error('Exception updating site status:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update site status' 
      }
    }
  }

  /**
   * Get sites for dropdown/select options
   * Returns simplified format for form controls
   */
  async getSiteOptions(activeOnly = false): Promise<{
    success: boolean
    data?: SiteOption[]
    error?: string
  }> {
    try {
      let query = this.supabase
        .from('sites')
        .select('site_id, site_name, status')
        .order('site_name', { ascending: true })
      
      if (activeOnly) {
        query = query.eq('status', SiteStatus.Active)
      }
      
      const { data, error } = await query
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      const options: SiteOption[] = data?.map((site: any) => ({
        value: site.site_id,
        label: site.site_name
      })) || []
      
      return { success: true, data: options }
    } catch (err) {
      console.error('Exception fetching site options:', err)
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch site options' 
      }
    }
  }

  /**
   * Validate site input data
   * Ensures all required fields are present and valid
   */
  private validateSiteInput(siteData: Omit<Site, 'siteId'>): { isValid: boolean; error?: string } {
    if (!siteData.siteName || siteData.siteName.trim().length === 0) {
      return { isValid: false, error: 'Site name is required' }
    }
    
    if (siteData.siteName.length > 100) {
      return { isValid: false, error: 'Site name must be less than 100 characters' }
    }
    
    if (!siteData.siteAddress) {
      return { isValid: false, error: 'Site address is required' }
    }
    
    if (!siteData.siteAddress.street || siteData.siteAddress.street.trim().length === 0) {
      return { isValid: false, error: 'Street address is required' }
    }
    
    if (!siteData.siteAddress.city || siteData.siteAddress.city.trim().length === 0) {
      return { isValid: false, error: 'City is required' }
    }
    
    if (!siteData.siteAddress.state || siteData.siteAddress.state.trim().length === 0) {
      return { isValid: false, error: 'State is required' }
    }
    
    if (!siteData.siteAddress.country || siteData.siteAddress.country.trim().length === 0) {
      return { isValid: false, error: 'Country is required' }
    }
    
    // Validate coordinates if provided
    if (typeof siteData.siteAddress.latitude === 'number') {
      if (siteData.siteAddress.latitude < -90 || siteData.siteAddress.latitude > 90) {
        return { isValid: false, error: 'Invalid latitude value' }
      }
    }
    
    if (typeof siteData.siteAddress.longitude === 'number') {
      if (siteData.siteAddress.longitude < -180 || siteData.siteAddress.longitude > 180) {
        return { isValid: false, error: 'Invalid longitude value' }
      }
    }
    
    if (!Object.values(SiteStatus).includes(siteData.status)) {
      return { isValid: false, error: 'Invalid site status' }
    }
    
    return { isValid: true }
  }

  /**
   * Check for duplicate site names
   */
  private async checkDuplicateSiteName(siteName: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('sites')
        .select('site_id')
        .ilike('site_name', siteName.trim())
        .limit(1)
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      if (data && data.length > 0) {
        return { success: false, error: 'A site with this name already exists' }
      }
      
      return { success: true }
    } catch (err) {
      return { success: false, error: 'Failed to check for duplicate site name' }
    }
  }

  /**
   * Validate and enhance address data
   * Can be extended to include geocoding services
   */
  private async validateAndEnhanceAddress(address: SiteAddress): Promise<SiteAddress> {
    // For now, just return the address as-is
    // In a production system, you might:
    // 1. Call a geocoding API to validate the address
    // 2. Auto-fill latitude/longitude if not provided
    // 3. Standardize address formatting
    
    const enhancedAddress = { ...address }
    
    // If coordinates are not provided, set defaults (could be enhanced with geocoding)
    if (typeof enhancedAddress.latitude !== 'number') {
      enhancedAddress.latitude = 0
    }
    
    if (typeof enhancedAddress.longitude !== 'number') {
      enhancedAddress.longitude = 0
    }
    
    return enhancedAddress
  }

  /**
   * Validate status transitions
   * Ensures proper workflow is followed
   */
  private validateStatusTransition(currentStatus: SiteStatus, newStatus: SiteStatus): boolean {
    const validTransitions: Record<SiteStatus, SiteStatus[]> = {
      [SiteStatus.Active]: [SiteStatus.Support, SiteStatus.NotActive],
      [SiteStatus.Support]: [SiteStatus.Active, SiteStatus.NotActive],
      [SiteStatus.NotActive]: [SiteStatus.Active, SiteStatus.Support, SiteStatus.Dismantled],
      [SiteStatus.Dismantled]: [] // No transitions from dismantled
    }
    
    return validTransitions[currentStatus]?.includes(newStatus) || false
  }

  /**
   * Check if site has active issues
   */
  private async checkActiveIssues(siteId: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('reports')
        .select('id')
        .eq('location', siteId)
        .neq('status', 'Resolved')
        .limit(1)
      
      if (error) {
        console.warn('Could not check for active issues:', error)
        return false
      }
      
      return data && data.length > 0
    } catch (err) {
      console.warn('Exception checking for active issues:', err)
      return false
    }
  }

  /**
   * Generate unique site ID
   */
  private generateSiteId(siteName: string): string {
    const sanitized = siteName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 20)
    
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 4)
    
    return `${sanitized}-${timestamp}-${random}`
  }

  /**
   * Transform database record to Site format
   */
  private transformDbRecordToSite(record: any): Site {
    return {
      siteId: record.site_id,
      siteName: record.site_name,
      siteAddress: record.site_address,
      status: record.status
    }
  }
}
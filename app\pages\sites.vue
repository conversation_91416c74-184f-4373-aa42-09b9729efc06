<template>
  <div class="p-6">
    <div class="text-center">
      <UIcon name="lucide:building" class="w-16 h-16 mx-auto mb-4 text-gray-400" />
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Sites Management</h1>
      <p class="text-gray-600">This page is under construction.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page metadata using layout functions
const setPageTitle = inject('setPageTitle') as (title: string) => void;
const setPageDescription = inject('setPageDescription') as (description: string) => void;
const setBreadcrumbs = inject('setBreadcrumbs') as (crumbs: Array<{ label: string, to?: string }>) => void;

// Set page information
setPageTitle('Sites Management');
setPageDescription('Manage and configure all your sites and locations');
setBreadcrumbs([
  { label: 'Sites Management' }
]);
</script>
/**
 * Sites data management composable
 * Handles all site-related operations for the desktop admin interface
 * Provides CRUD operations and real-time updates for site management
 */

import type { RealtimeChannel } from '@supabase/supabase-js'
import type { Site, SiteAddress, SiteOption } from '~/types'
import { SiteStatus } from '~/types'
import type { AdminQueryOptions } from '~/types/supabase'

export const useSites = () => {
  // Get Supabase client and authentication
  const { supabase, executeAdminQuery, setupRealtimeSubscription } = useSupabase()
  const { hasPermission } = useAuth()
  
  // Reactive state for sites management
  const sites = ref<Site[]>([])
  const isLoading = ref(false)
  const isCreating = ref(false)
  const isUpdating = ref(false)
  const error = ref<string | null>(null)
  const totalCount = ref(0)
  const realtimeChannel = ref<RealtimeChannel | null>(null)
  
  // Filters and search state
  const filters = ref({
    status: null as SiteStatus | null,
    search: '',
    city: null as string | null,
    state: null as string | null,
    country: null as string | null
  })
  
  // Pagination state
  const pagination = ref({
    page: 1,
    pageSize: 25,
    orderBy: 'site_name' as keyof Site,
    ascending: true
  })

  /**
   * Fetch all sites with admin access and filtering
   * Returns all sites for comprehensive management
   */
  const fetchSites = async (refreshCache = false): Promise<void> => {
    try {
      isLoading.value = true
      error.value = null
      
      console.log('Fetching sites with filters:', filters.value)
      
      const options: AdminQueryOptions = {
        includeAllDevices: true,
        orderBy: pagination.value.orderBy as any,
        ascending: pagination.value.ascending,
        limit: pagination.value.pageSize,
        offset: (pagination.value.page - 1) * pagination.value.pageSize
      }
      
      const result = await executeAdminQuery(async (client) => {
        let query = client
          .from('sites')
          .select('*', { count: 'exact' })
        
        // Apply status filter
        if (filters.value.status) {
          query = query.eq('status', filters.value.status)
        }
        
        // Apply search filter (search in site name)
        if (filters.value.search) {
          query = query.ilike('site_name', `%${filters.value.search}%`)
        }
        
        // Apply location filters (on JSONB address field)
        if (filters.value.city) {
          query = query.eq('site_address->>city', filters.value.city)
        }
        
        if (filters.value.state) {
          query = query.eq('site_address->>state', filters.value.state)
        }
        
        if (filters.value.country) {
          query = query.eq('site_address->>country', filters.value.country)
        }
        
        // Apply ordering
        query = query.order(options.orderBy || 'site_name', { ascending: options.ascending })
        
        // Apply pagination
        if (options.limit) {
          query = query.limit(options.limit)
        }
        
        if (options.offset) {
          query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
        }
        
        return await query
      })
      
      if (result.success && result.data) {
        // Transform database records to Site format
        sites.value = transformDbRecordsToSites(result.data.data || [])
        totalCount.value = result.data.count || 0
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`✅ Fetched ${sites.value.length} sites (${totalCount.value} total)`)
        }
      } else {
        console.error('Failed to fetch sites:', result.error)
        error.value = result.error || 'Failed to fetch sites'
      }
    } catch (err) {
      console.error('Exception while fetching sites:', err)
      error.value = err instanceof Error ? err.message : 'Failed to fetch sites'
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Create a new site
   * Validates address data and geographic coordinates
   */
  const createSite = async (siteData: Omit<Site, 'siteId'>): Promise<{ success: boolean; data?: Site; error?: string }> => {
    try {
      if (!hasPermission('sites.create')) {
        return { success: false, error: 'Permission denied: Cannot create sites' }
      }
      
      isCreating.value = true
      error.value = null
      
      console.log('Creating new site:', siteData.siteName)
      
      // Generate site ID (using timestamp + random for uniqueness)
      const siteId = `site-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      
      const newSite = {
        site_id: siteId,
        site_name: siteData.siteName,
        site_address: siteData.siteAddress,
        status: siteData.status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('sites')
          .insert([newSite])
          .select()
          .single()
      })
      
      if (result.success && result.data) {
        const createdSite = transformDbRecordToSite(result.data)
        sites.value.unshift(createdSite)
        totalCount.value += 1
        
        console.log('✅ Site created successfully:', createdSite.siteId)
        return { success: true, data: createdSite }
      } else {
        console.error('Failed to create site:', result.error)
        return { success: false, error: result.error || 'Failed to create site' }
      }
    } catch (err) {
      console.error('Exception while creating site:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to create site'
      return { success: false, error: errorMessage }
    } finally {
      isCreating.value = false
    }
  }

  /**
   * Update an existing site
   * Handles status changes and address updates
   */
  const updateSite = async (siteId: string, updates: Partial<Site>): Promise<{ success: boolean; data?: Site; error?: string }> => {
    try {
      if (!hasPermission('sites.update')) {
        return { success: false, error: 'Permission denied: Cannot update sites' }
      }
      
      isUpdating.value = true
      error.value = null
      
      console.log('Updating site:', siteId)
      
      const updateData = {
        site_name: updates.siteName,
        site_address: updates.siteAddress,
        status: updates.status,
        updated_at: new Date().toISOString()
      }
      
      // Remove undefined fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof typeof updateData] === undefined) {
          delete updateData[key as keyof typeof updateData]
        }
      })
      
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('sites')
          .update(updateData)
          .eq('site_id', siteId)
          .select()
          .single()
      })
      
      if (result.success && result.data) {
        const updatedSite = transformDbRecordToSite(result.data)
        
        // Update local state
        const index = sites.value.findIndex(site => site.siteId === siteId)
        if (index !== -1) {
          sites.value[index] = updatedSite
        }
        
        console.log('✅ Site updated successfully:', siteId)
        return { success: true, data: updatedSite }
      } else {
        console.error('Failed to update site:', result.error)
        return { success: false, error: result.error || 'Failed to update site' }
      }
    } catch (err) {
      console.error('Exception while updating site:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update site'
      return { success: false, error: errorMessage }
    } finally {
      isUpdating.value = false
    }
  }

  /**
   * Delete a site
   * Checks for associated issues before deletion
   */
  const deleteSite = async (siteId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!hasPermission('sites.delete')) {
        return { success: false, error: 'Permission denied: Cannot delete sites' }
      }
      
      console.log('Deleting site:', siteId)
      
      // Check for associated issues first
      const issuesResult = await executeAdminQuery(async (client) => {
        return await client
          .from('reports')
          .select('id')
          .eq('location', siteId)
          .limit(1)
      })
      
      if (issuesResult.success && issuesResult.data && issuesResult.data.length > 0) {
        return { success: false, error: 'Cannot delete site: Associated issues exist' }
      }
      
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('sites')
          .delete()
          .eq('site_id', siteId)
      })
      
      if (result.success) {
        // Remove from local state
        sites.value = sites.value.filter(site => site.siteId !== siteId)
        totalCount.value -= 1
        
        console.log('✅ Site deleted successfully:', siteId)
        return { success: true }
      } else {
        console.error('Failed to delete site:', result.error)
        return { success: false, error: result.error || 'Failed to delete site' }
      }
    } catch (err) {
      console.error('Exception while deleting site:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete site'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Get site by ID with full details
   */
  const getSiteById = async (siteId: string): Promise<Site | null> => {
    try {
      const result = await executeAdminQuery(async (client) => {
        return await client
          .from('sites')
          .select('*')
          .eq('site_id', siteId)
          .single()
      })
      
      if (result.success && result.data) {
        return transformDbRecordToSite(result.data)
      }
      
      return null
    } catch (err) {
      console.error('Error fetching site by ID:', err)
      return null
    }
  }

  /**
   * Get sites as options for dropdowns
   * Returns simplified format for form selectors
   */
  const getSiteOptions = (): SiteOption[] => {
    return sites.value.map(site => ({
      value: site.siteId,
      label: site.siteName
    }))
  }

  /**
   * Get active sites only
   */
  const getActiveSites = (): Site[] => {
    return sites.value.filter(site => site.status === 'Active')
  }

  /**
   * Apply filters and refresh sites
   */
  const applyFilters = async (newFilters: typeof filters.value) => {
    filters.value = { ...newFilters }
    pagination.value.page = 1 // Reset to first page
    await fetchSites()
  }

  /**
   * Clear all filters
   */
  const clearFilters = async () => {
    filters.value = {
      status: null,
      search: '',
      city: null,
      state: null,
      country: null
    }
    pagination.value.page = 1
    await fetchSites()
  }

  /**
   * Setup real-time updates for sites
   */
  const setupRealtimeUpdates = () => {
    if (realtimeChannel.value) {
      console.log('Real-time channel already active for sites')
      return
    }
    
    realtimeChannel.value = setupRealtimeSubscription('sites', (payload) => {
      console.log('Real-time site update:', payload)
      
      if (payload.eventType === 'INSERT') {
        const newSite = transformDbRecordToSite(payload.new)
        sites.value.unshift(newSite)
        totalCount.value += 1
      } else if (payload.eventType === 'UPDATE') {
        const updatedSite = transformDbRecordToSite(payload.new)
        const index = sites.value.findIndex(site => site.siteId === updatedSite.siteId)
        if (index !== -1) {
          sites.value[index] = updatedSite
        }
      } else if (payload.eventType === 'DELETE') {
        sites.value = sites.value.filter(site => site.siteId !== payload.old.site_id)
        totalCount.value -= 1
      }
    })
  }

  /**
   * Transform database record to Site format
   */
  const transformDbRecordToSite = (record: any): Site => {
    return {
      siteId: record.site_id,
      siteName: record.site_name,
      siteAddress: record.site_address,
      status: record.status
    }
  }

  /**
   * Transform multiple database records
   */
  const transformDbRecordsToSites = (records: any[]): Site[] => {
    return records.map(transformDbRecordToSite)
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    if (realtimeChannel.value) {
      realtimeChannel.value.unsubscribe()
      realtimeChannel.value = null
    }
  }

  // Initialize on mount
  onMounted(async () => {
    await fetchSites()
    setupRealtimeUpdates()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    sites: readonly(sites),
    isLoading: readonly(isLoading),
    isCreating: readonly(isCreating),
    isUpdating: readonly(isUpdating),
    error: readonly(error),
    totalCount: readonly(totalCount),
    filters: readonly(filters),
    pagination: readonly(pagination),
    
    // Actions
    fetchSites,
    createSite,
    updateSite,
    deleteSite,
    getSiteById,
    
    // Utilities
    getSiteOptions,
    getActiveSites,
    
    // Filtering
    applyFilters,
    clearFilters,
    
    // Real-time
    setupRealtimeUpdates,
    cleanup
  }
}
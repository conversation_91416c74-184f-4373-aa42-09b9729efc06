/**
 * Authentication management composable with admin permissions
 * Handles login, logout, session management, and role-based access control
 * Designed for desktop admin interface with elevated privileges
 */

import type { User } from '@supabase/supabase-js'
import type { UserRole } from '~/types'

export const useAuth = () => {
  // Get Supabase client and user from Nuxt module
  const supabase = useSupabaseClient()
  const supabaseUser = useSupabaseUser()
  
  // Reactive authentication state
  const isAuthenticated = ref(false)
  const isLoading = ref(true)
  const user = ref<User | null>(null)
  const userRole = ref<UserRole | null>(null)
  const authError = ref<string | null>(null)
  const sessionExpiry = ref<string | null>(null)
  
  // Admin role hierarchy for permission checking
  const roleHierarchy: Record<UserRole, number> = {
    'User': 1,
    'Technician': 2,
    'Manager': 3,
    'Admin': 4
  }

  /**
   * Initialize authentication state
   * Checks current session and user permissions
   */
  const initialize = async () => {
    try {
      isLoading.value = true
      if (process.env.NODE_ENV === 'development') {
        console.log('Initializing authentication...')
      }
      
      // Get current session
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('Session error:', error)
        authError.value = error.message
        return
      }
      
      if (session?.user) {
        console.log('✅ Active session found')
        await updateUserState(session.user)
        sessionExpiry.value = session.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
      } else {
        console.log('No active session')
        clearUserState()
      }
    } catch (err) {
      console.error('Auth initialization error:', err)
      authError.value = err instanceof Error ? err.message : 'Authentication initialization failed'
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Admin login with email and password
   * Validates admin permissions after successful authentication
   */
  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      isLoading.value = true
      authError.value = null
      console.log('Attempting admin login...')
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) {
        console.error('Login error:', error)
        authError.value = error.message
        return { success: false, error: error.message }
      }
      
      if (data.user) {
        console.log('✅ Login successful')
        await updateUserState(data.user)
        
        // Verify admin permissions
        if (!hasAdminAccess()) {
          console.error('User does not have admin access')
          await logout()
          authError.value = 'Access denied: Admin privileges required'
          return { success: false, error: 'Access denied: Admin privileges required' }
        }
        
        return { success: true }
      }
      
      return { success: false, error: 'Login failed' }
    } catch (err) {
      console.error('Login exception:', err)
      const errorMessage = err instanceof Error ? err.message : 'Login failed'
      authError.value = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Logout current user
   * Clears all authentication state
   */
  const logout = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      isLoading.value = true
      console.log('Logging out...')
      
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        console.error('Logout error:', error)
        authError.value = error.message
        return { success: false, error: error.message }
      }
      
      clearUserState()
      console.log('✅ Logout successful')
      return { success: true }
    } catch (err) {
      console.error('Logout exception:', err)
      const errorMessage = err instanceof Error ? err.message : 'Logout failed'
      authError.value = errorMessage
      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Update user state after successful authentication
   * Fetches user profile and role information
   */
  const updateUserState = async (authUser: User) => {
    try {
      user.value = authUser
      isAuthenticated.value = true
      
      // Extract role from user metadata or default to User
      // In a real implementation, this would come from a profiles table
      userRole.value = (authUser.user_metadata?.role as UserRole) || 'User'
      
      console.log('User state updated:', {
        id: authUser.id,
        email: authUser.email,
        role: userRole.value
      })
      
      authError.value = null
    } catch (err) {
      console.error('Error updating user state:', err)
      authError.value = err instanceof Error ? err.message : 'Failed to update user state'
    }
  }

  /**
   * Clear all authentication state
   */
  const clearUserState = () => {
    user.value = null
    userRole.value = null
    isAuthenticated.value = false
    sessionExpiry.value = null
    authError.value = null
  }

  /**
   * Check if user has admin access
   * Verifies user role allows admin operations
   */
  const hasAdminAccess = (): boolean => {
    return userRole.value === 'Admin' || userRole.value === 'Manager'
  }

  /**
   * Check if user has specific role or higher
   * Uses role hierarchy for permission checking
   */
  const hasRole = (requiredRole: UserRole): boolean => {
    if (!userRole.value) return false
    
    const userLevel = roleHierarchy[userRole.value]
    const requiredLevel = roleHierarchy[requiredRole]
    
    return userLevel >= requiredLevel
  }

  /**
   * Check if user has permission for specific action
   * Implements granular permission checking
   */
  const hasPermission = (permission: string): boolean => {
    if (!isAuthenticated.value || !userRole.value) return false
    
    // Admin has all permissions
    if (userRole.value === 'Admin') return true
    
    // Define permission mappings
    const permissions: Record<UserRole, string[]> = {
      'Admin': ['*'], // All permissions
      'Manager': [
        'issues.read', 'issues.create', 'issues.update', 'issues.delete',
        'sites.read', 'sites.create', 'sites.update',
        'users.read', 'reports.read', 'reports.export'
      ],
      'Technician': [
        'issues.read', 'issues.update',
        'sites.read', 'reports.read'
      ],
      'User': [
        'issues.read', 'sites.read', 'reports.read'
      ]
    }
    
    const userPermissions = permissions[userRole.value] || []
    return userPermissions.includes('*') || userPermissions.includes(permission)
  }

  /**
   * Refresh authentication token
   * Extends session validity
   */
  const refreshSession = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('Refreshing session...')
      
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Session refresh error:', error)
        authError.value = error.message
        return { success: false, error: error.message }
      }
      
      if (data.session) {
        console.log('✅ Session refreshed')
        sessionExpiry.value = data.session.expires_at ? new Date(data.session.expires_at * 1000).toISOString() : null
        return { success: true }
      }
      
      return { success: false, error: 'Session refresh failed' }
    } catch (err) {
      console.error('Session refresh exception:', err)
      const errorMessage = err instanceof Error ? err.message : 'Session refresh failed'
      authError.value = errorMessage
      return { success: false, error: errorMessage }
    }
  }

  // Set up auth state change listener
  const setupAuthListener = () => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event)
      
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        if (session?.user) {
          await updateUserState(session.user)
          sessionExpiry.value = session.expires_at ? new Date(session.expires_at * 1000).toISOString() : null
        }
      } else if (event === 'SIGNED_OUT') {
        clearUserState()
      }
    })
    
    return subscription
  }

  // Initialize on composable creation
  onMounted(async () => {
    await initialize()
    setupAuthListener()
  })

  // Watch for changes in supabaseUser from Nuxt module
  watch(supabaseUser, async (newUser) => {
    if (newUser && newUser !== user.value) {
      await updateUserState(newUser)
    } else if (!newUser && user.value) {
      clearUserState()
    }
  }, { immediate: true })

  return {
    // State
    isAuthenticated: readonly(isAuthenticated),
    isLoading: readonly(isLoading),
    user: readonly(user),
    userRole: readonly(userRole),
    authError: readonly(authError),
    sessionExpiry: readonly(sessionExpiry),
    
    // Actions
    login,
    logout,
    refreshSession,
    
    // Permission checks
    hasAdminAccess,
    hasRole,
    hasPermission,
    
    // Utilities
    initialize,
    setupAuthListener
  }
}